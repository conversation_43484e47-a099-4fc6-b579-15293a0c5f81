-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _design = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design")
local COLORS = _design.COLORS
local SIZES = _design.SIZES
local BORDER_RADIUS = _design.BORDER_RADIUS
local ZIndexManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "ZIndexManager").ZIndexManager
local function Toast(props)
	local isVisible, setIsVisible = React.useState(false)
	local isRemoving, setIsRemoving = React.useState(false)
	-- Auto-dismiss logic
	local handleDismiss
	React.useEffect(function()
		-- Animate in
		task.delay(0.1, function()
			return setIsVisible(true)
		end)
		-- Auto-dismiss if duration is set
		if props.toast.duration ~= nil and props.toast.duration > 0 then
			task.delay(props.toast.duration / 1000, function()
				handleDismiss()
			end)
		end
	end, {})
	handleDismiss = function()
		setIsRemoving(true)
		setIsVisible(false)
		task.delay(0.3, function()
			props.onDismiss(props.toast.id)
		end)
	end
	local getTypeColors = function()
		local _exp = props.toast.type
		repeat
			if _exp == "success" then
				return {
					background = COLORS.green,
					border = COLORS.border.base,
					icon = "✓",
					textColor = COLORS.text.main,
				}
			end
			if _exp == "error" then
				return {
					background = COLORS.error,
					border = COLORS.border.base,
					icon = "✕",
					textColor = COLORS.text.main,
				}
			end
			if _exp == "warning" then
				return {
					background = COLORS.yellow,
					border = COLORS.border.base,
					icon = "⚠",
					textColor = COLORS.text.inverse,
				}
			end
			if _exp == "info" then
				return {
					background = COLORS["toast-info"],
					border = COLORS["toast-info"],
					icon = "ℹ",
					textColor = COLORS.text.inverse,
				}
			end
		until true
	end
	local typeColors = getTypeColors()
	local yOffset = props.index * 80
	local transparency = if isVisible and not isRemoving then 0 else 1
	return React.createElement("frame", {
		Size = UDim2.new(0, 300, 0, 70),
		Position = UDim2.new(1, -20, 0, 20 + yOffset),
		AnchorPoint = Vector2.new(1, 0),
		BackgroundColor3 = Color3.fromHex(typeColors.background),
		BackgroundTransparency = transparency,
		BorderSizePixel = 0,
		ZIndex = ZIndexManager:getCurrentZIndex() + 100,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.lg),
	}), React.createElement("uistroke", {
		Color = Color3.fromHex(typeColors.border),
		Thickness = 2,
		Transparency = transparency,
	}), React.createElement("frame", {
		Size = UDim2.new(1, 4, 1, 4),
		Position = UDim2.new(0, 2, 0, 2),
		AnchorPoint = Vector2.new(0, 0),
		BackgroundColor3 = Color3.fromRGB(0, 0, 0),
		BackgroundTransparency = 0.7,
		BorderSizePixel = 0,
		ZIndex = -1,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.lg),
	})), React.createElement("frame", {
		Size = UDim2.new(0, 4, 1, 0),
		Position = UDim2.new(0, 0, 0, 0),
		BackgroundColor3 = Color3.fromHex(typeColors.border),
		BackgroundTransparency = transparency,
		BorderSizePixel = 0,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.sm),
	})), React.createElement("frame", {
		Size = UDim2.new(1, -50, 1, 0),
		Position = UDim2.new(0, 16, 0, 0),
		BackgroundTransparency = 1,
	}, React.createElement("textlabel", {
		Text = typeColors.icon,
		Size = UDim2.new(0, 20, 0, 20),
		Position = UDim2.new(0, 0, 0, 8),
		BackgroundTransparency = 1,
		TextColor3 = Color3.fromHex(typeColors.textColor),
		TextSize = 16,
		Font = Enum.Font.Gotham,
		TextXAlignment = Enum.TextXAlignment.Center,
		TextYAlignment = Enum.TextYAlignment.Center,
		TextTransparency = transparency,
	}), React.createElement("textlabel", {
		Text = props.toast.title,
		Size = UDim2.new(1, -30, 0, 20),
		Position = UDim2.new(0, 30, 0, 8),
		BackgroundTransparency = 1,
		TextColor3 = Color3.fromHex(typeColors.textColor),
		TextSize = SIZES.fontSize.md,
		Font = Enum.Font.Gotham,
		TextXAlignment = Enum.TextXAlignment.Left,
		TextYAlignment = Enum.TextYAlignment.Center,
		TextTransparency = transparency,
		TextTruncate = Enum.TextTruncate.AtEnd,
	}), props.toast.message ~= nil and (React.createElement("textlabel", {
		Text = props.toast.message,
		Size = UDim2.new(1, -30, 0, 15),
		Position = UDim2.new(0, 30, 0, 30),
		BackgroundTransparency = 1,
		TextColor3 = Color3.fromHex(typeColors.textColor),
		TextSize = 12,
		Font = Enum.Font.Gotham,
		TextXAlignment = Enum.TextXAlignment.Left,
		TextYAlignment = Enum.TextYAlignment.Center,
		TextTransparency = transparency,
		TextTruncate = Enum.TextTruncate.AtEnd,
	})), props.toast.action and (React.createElement("textbutton", {
		Text = props.toast.action.label,
		Size = UDim2.new(0, 60, 0, 18),
		Position = UDim2.new(1, -70, 0, 48),
		BackgroundColor3 = Color3.fromHex(typeColors.background),
		BackgroundTransparency = transparency,
		TextColor3 = Color3.fromHex(COLORS.text.main),
		TextSize = 11,
		Font = Enum.Font.Gotham,
		AutoButtonColor = false,
		BorderSizePixel = 0,
		Event = {
			Activated = function()
				props.toast.action.onClick()
				handleDismiss()
			end,
		},
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.sm),
	})))), React.createElement("textbutton", {
		Text = "×",
		Size = UDim2.new(0, 20, 0, 20),
		Position = UDim2.new(1, -25, 0, 5),
		BackgroundTransparency = 1,
		TextColor3 = Color3.fromHex(COLORS.text.secondary),
		TextSize = 18,
		Font = Enum.Font.Gotham,
		AutoButtonColor = false,
		BorderSizePixel = 0,
		Event = {
			Activated = handleDismiss,
		},
	}))
end
return {
	Toast = Toast,
}
