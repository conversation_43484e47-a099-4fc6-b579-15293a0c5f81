import * as React from "@rbxts/react";
import { COLORS, SIZES, TYPOGRAPHY, BORDER_RADIUS } from "../../design";

interface FormFieldProps {
	label?: string;
	value: string;
	onChange: (value: string) => void;
	onBlur?: () => void;
	placeholder?: string;
	error?: string;
	required?: boolean;
	disabled?: boolean;
	type?: "text" | "password" | "number";
	maxLength?: number;
	layoutOrder?: number;
}

export const FormField = React.memo((props: FormFieldProps): React.ReactElement => {
	const [focused, setFocused] = React.useState(false);
	const [hasInteracted, setHasInteracted] = React.useState(false);

	// Memoize validation state
	const validationState = React.useMemo(() => {
		const hasError = props.error !== undefined && props.error !== "";
		const isRequired = props.required === true;
		const isEmpty = props.value === "";
		const showError = hasError && hasInteracted;
		const showRequiredIndicator = isRequired && !hasError;

		return {
			hasError,
			isEmpty,
			showError,
			showRequiredIndicator,
		};
	}, [props.error, props.required, props.value, hasInteracted]);

	// Memoize colors based on state
	const colors = React.useMemo(() => {
		const { hasError, showError } = validationState;

		return {
			border: showError
				? COLORS.border.danger
				: focused
					? COLORS.border.focus
					: COLORS.border.l2,
			background: props.disabled
				? COLORS.bg.secondary
				: focused
					? COLORS.bg.surface
					: COLORS.bg.base,
			text: props.disabled ? COLORS.text.secondary : COLORS.text.main,
			label: hasError ? COLORS.error : COLORS.text.secondary,
		};
	}, [validationState, focused, props.disabled]);

	// Handle blur with validation
	const handleBlur = React.useCallback(() => {
		setFocused(false);
		setHasInteracted(true);
		props.onBlur?.();
	}, [props.onBlur]);

	// Handle focus
	const handleFocus = React.useCallback(() => {
		setFocused(true);
	}, []);

	// Handle text change with validation
	const handleTextChanged = React.useCallback(
		(textBox: TextBox) => {
			let newValue = textBox.Text;

			// Apply max length constraint
			if (props.maxLength !== undefined && newValue.size() > props.maxLength) {
				newValue = newValue.sub(1, props.maxLength);
				textBox.Text = newValue;
			}

			// Type-specific validation
			if (props.type === "number") {
				// Allow only numbers and decimal point
				const numbersOnly = newValue.gsub("[^%d%.]", "")[0];
				if (numbersOnly !== newValue) {
					newValue = numbersOnly;
					textBox.Text = newValue;
				}
			}

			props.onChange(newValue);
		},
		[props.onChange, props.maxLength, props.type],
	);

	const fieldHeight = 36;
	const labelHeight = props.label ? 20 : 0;
	const errorHeight = validationState.showError ? 18 : 0;
	const totalHeight = labelHeight + fieldHeight + errorHeight + (labelHeight > 0 ? 4 : 0) + (errorHeight > 0 ? 4 : 0);

	return (
		<frame
			Size={new UDim2(1, 0, 0, totalHeight)}
			BackgroundTransparency={1}
			LayoutOrder={props.layoutOrder}
		>
			{/* Label */}
			{props.label && (
				<textlabel
					Text={props.label + (validationState.showRequiredIndicator ? " *" : "")}
					Size={new UDim2(1, 0, 0, labelHeight)}
					Position={new UDim2(0, 0, 0, 0)}
					BackgroundTransparency={1}
					TextColor3={Color3.fromHex(colors.label)}
					TextSize={13}
					Font={Enum.Font.Gotham}
					TextXAlignment={Enum.TextXAlignment.Left}
					TextYAlignment={Enum.TextYAlignment.Center}
				/>
			)}

			{/* Input Field */}
			<textbox
				Text={props.value}
				PlaceholderText={props.placeholder ?? ""}
				Size={new UDim2(1, 0, 0, fieldHeight)}
				Position={new UDim2(0, 0, 0, labelHeight + (props.label ? 4 : 0))}
				BackgroundColor3={Color3.fromHex(colors.background)}
				BorderSizePixel={0}
				TextColor3={Color3.fromHex(colors.text)}
				PlaceholderColor3={Color3.fromHex(COLORS.text.muted)}
				TextSize={SIZES.fontSize.md}
				Font={Enum.Font.Gotham}
				TextXAlignment={Enum.TextXAlignment.Left}
				ClearTextOnFocus={false}
				TextEditable={!props.disabled}
				Event={{
					Focused: handleFocus,
					FocusLost: handleBlur,
				}}
				Change={{
					Text: handleTextChanged,
				}}
			>
				<uicorner CornerRadius={new UDim(0, BORDER_RADIUS.sm)} />
				<uistroke
					Color={Color3.fromHex(colors.border)}
					Thickness={focused ? 2 : 1}
					Transparency={0}
				/>
				<uipadding
					PaddingLeft={new UDim(0, 12)}
					PaddingRight={new UDim(0, 12)}
					PaddingTop={new UDim(0, 8)}
					PaddingBottom={new UDim(0, 8)}
				/>
			</textbox>

			{/* Error Message */}
			{validationState.showError && (
				<textlabel
					Text={props.error!}
					Size={new UDim2(1, 0, 0, errorHeight)}
					Position={new UDim2(0, 0, 0, labelHeight + fieldHeight + (props.label ? 8 : 4))}
					BackgroundTransparency={1}
					TextColor3={Color3.fromHex(COLORS.error)}
					TextSize={12}
					Font={Enum.Font.Gotham}
					TextXAlignment={Enum.TextXAlignment.Left}
					TextYAlignment={Enum.TextYAlignment.Center}
				/>
			)}
		</frame>
	);
});

// Validation helpers
export const validators = {
	required: (value: string) => (value.size() === 0 ? "This field is required" : undefined),
	
	minLength: (min: number) => (value: string) =>
		value.size() < min ? `Must be at least ${min} characters` : undefined,
	
	maxLength: (max: number) => (value: string) =>
		value.size() > max ? `Must be no more than ${max} characters` : undefined,
	
	email: (value: string) => {
		const emailPattern = "^[%w%._%+-]+@[%w%.%-]+%.[%a]+$";
		return value.match(emailPattern)[0] ? undefined : "Please enter a valid email address";
	},
	
	number: (value: string) => {
		const num = tonumber(value);
		return num !== undefined ? undefined : "Please enter a valid number";
	},
	
	range: (min: number, max: number) => (value: string) => {
		const num = tonumber(value);
		if (num === undefined) return "Please enter a valid number";
		if (num < min || num > max) return `Value must be between ${min} and ${max}`;
		return undefined;
	},
};

// Simplified form validation for roblox-ts
export function useFormValidation<T extends Record<string, string>>(
	initialValues: T,
	fieldNames: (keyof T)[],
	validationRules: Record<keyof T, ((value: string) => string | undefined)[]>
) {
	const [values, setValues] = React.useState(initialValues);
	const [errors, setErrors] = React.useState<Partial<Record<keyof T, string>>>({});
	const [touchedFields, setTouchedFields] = React.useState<Record<keyof T, boolean>>({} as Record<keyof T, boolean>);

	// Validate a single field
	const validateField = React.useCallback((field: keyof T, value: string) => {
		const rules = validationRules[field] || [];
		
		for (let i = 0; i < rules.size(); i++) {
			const rule = rules[i];
			const validationError = rule(value);
			if (validationError) {
				return validationError;
			}
		}
		return undefined;
	}, [validationRules]);

	// Set field value with validation
	const setValue = React.useCallback((field: keyof T, value: string) => {
		setValues((prev) => ({ ...prev, [field]: value }));
		
		// Validate immediately if field has been touched
		if (touchedFields[field]) {
			const validationError = validateField(field, value);
			setErrors((prev) => ({ ...prev, [field]: validationError }));
		}
	}, [touchedFields, validateField]);

	// Mark field as touched and validate
	const setFieldTouched = React.useCallback((field: keyof T) => {
		setTouchedFields((prev) => ({ ...prev, [field]: true }));
		const validationError = validateField(field, values[field] as string);
		setErrors((prev) => ({ ...prev, [field]: validationError }));
	}, [validateField, values]);

	// Validate all fields
	const validateAll = React.useCallback(() => {
		const newErrors: Partial<Record<keyof T, string>> = {};
		let hasErrors = false;
		
		// Validate each field by name
		for (let i = 0; i < fieldNames.size(); i++) {
			const field = fieldNames[i];
			const validationError = validateField(field, values[field] as string);
			if (validationError) {
				newErrors[field] = validationError;
				hasErrors = true;
			}
		}

		setErrors(newErrors);
		return !hasErrors;
	}, [fieldNames, validateField, values]);

	// Check if form is valid
	const isValid = React.useMemo(() => {
		// Check each field for errors
		for (let i = 0; i < fieldNames.size(); i++) {
			const field = fieldNames[i];
			if (errors[field] !== undefined && errors[field] !== "") {
				return false;
			}
		}
		return true;
	}, [errors, fieldNames]);

	return {
		values,
		errors,
		touched: touchedFields,
		isValid,
		setValue,
		setTouched: setFieldTouched,
		validateAll,
	};
}