import * as React from "@rbxts/react";
interface LabelProps {
    text: string;
    textColor?: string;
    fontSize?: number;
    alignment?: Enum.TextXAlignment;
    verticalAlignment?: Enum.TextYAlignment;
    size?: UDim2;
    position?: UDim2;
    anchorPoint?: Vector2;
    layoutOrder?: number;
    bold?: boolean;
    autoSize?: boolean;
    textWrapped?: boolean;
    backgroundColor?: string;
    backgroundTransparency?: number;
    padding?: number;
    zIndex?: number;
}
export declare function Label(props: LabelProps): React.ReactElement;
export {};
