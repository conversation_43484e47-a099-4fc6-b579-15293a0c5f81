import * as React from "@rbxts/react";
import { Modal, Button, Label, VerticalFrame, HorizontalFrame, COLORS, ToastService, ProgressBar } from "../../core";
import { QuickUtils } from "../../core/helper/GameUtils";
import { ElementalBattleGame, ElementalBattleState } from "../../core/game/ElementalBattleGame";

interface ElementalBattleUIProps {
	isOpen: boolean;
	onClose: () => void;
}

export function ElementalBattleUI({ isOpen, onClose }: ElementalBattleUIProps): React.ReactElement {
	const [battleGame] = React.useState(() => new ElementalBattleGame());
	const [gameState, setGameState] = React.useState<ElementalBattleState>(() => battleGame.getGameState());

	// Poll for state changes
	React.useEffect(() => {
		const heartbeat = game.GetService("RunService").Heartbeat.Connect(() => {
			setGameState(battleGame.getGameState());
		});

		return () => heartbeat.Disconnect();
	}, [battleGame]);

	const handleStartGame = React.useCallback(async () => {
		try {
			print("⚔️ [ElementalBattleUI] Starting Elemental Battle...");

			const result = await battleGame.start();

			if (result.isOk()) {
				ToastService.showSuccess("⚔️ Battle Started!", "Prepare for elemental combat!");
			} else {
				const errorResult = result.getError();
				ToastService.showError("❌ Start Failed", errorResult.message);
			}
		} catch (err) {
			ToastService.showError("❌ Error", `Failed to start game: ${err}`);
		}
	}, [battleGame]);

	const handleStopGame = React.useCallback(async () => {
		try {
			print("⚔️ [ElementalBattleUI] Stopping Elemental Battle...");

			const result = await battleGame.stop();

			if (result.isOk()) {
				ToastService.showSuccess("⚔️ Battle Ended", "Thanks for playing!");
			} else {
				const errorResult = result.getError();
				ToastService.showError("❌ Stop Failed", errorResult.message);
			}
		} catch (err) {
			ToastService.showError("❌ Error", `Failed to stop game: ${err}`);
		}
	}, [battleGame]);

	const handleClose = React.useCallback(() => {
		if (gameState.isGameActive) {
			ToastService.showWarning("⚠️ Game Active", "Stop the game before closing the panel");
			return;
		}
		onClose();
	}, [gameState.isGameActive, onClose]);

	// Calculate health percentage for progress bar
	const healthPercentage =
		gameState.maxPlayerHealth > 0 ? (gameState.playerHealth / gameState.maxPlayerHealth) * 100 : 0;
	const experiencePercentage =
		gameState.experienceToNextLevel > 0 ? (gameState.experience / gameState.experienceToNextLevel) * 100 : 0;

	// Get phase display text
	const getPhaseDisplayText = () => {
		switch (gameState.gamePhase) {
			case "waiting":
				return "Ready to Start";
			case "playing":
				return `Wave ${gameState.currentWave} - ${gameState.enemiesRemaining} enemies left`;
			case "betweenWaves":
				return `Next wave in ${math.ceil(gameState.timeUntilNextWave)}s`;
			case "ended":
				return "Game Over";
			default:
				return "Unknown";
		}
	};

	return (
		<Modal isOpen={isOpen} title="⚔️ Elemental Battle Arena" width={500} height={600} onClose={handleClose}>
			<VerticalFrame spacing={16} padding={16}>
				{/* Game Status Section */}
				<VerticalFrame spacing={8}>
					<Label text="🎮 Game Status" fontSize={18} bold={true} />

					<Label
						text={`Status: ${getPhaseDisplayText()}`}
						fontSize={14}
						textColor={gameState.isGameActive ? COLORS.success : COLORS.text.secondary}
					/>

					<Label
						text={`Score: ${gameState.score} | Level: ${gameState.playerLevel}`}
						fontSize={14}
						textColor={COLORS.primary}
					/>
				</VerticalFrame>

				{/* Player Stats Section */}
				{gameState.isGameActive && (
					<VerticalFrame spacing={8}>
						<Label text="⚡ Player Stats" fontSize={16} bold={true} />

						{/* Health Bar */}
						<VerticalFrame spacing={4}>
							<Label
								text={`❤️ Health: ${gameState.playerHealth}/${gameState.maxPlayerHealth}`}
								fontSize={12}
							/>

							<ProgressBar
								progress={healthPercentage / 100}
								barColor={
									healthPercentage > 60
										? QuickUtils.safeColor(COLORS.success)
										: healthPercentage > 30
											? QuickUtils.safeColor(COLORS.warning)
											: QuickUtils.safeColor(COLORS.error)
								}
								backgroundColor={QuickUtils.safeColor(COLORS.bg.secondary)}
								size={new UDim2(1, 0, 0, 20)}
							/>
						</VerticalFrame>

						{/* Experience Bar */}
						<VerticalFrame spacing={4}>
							<Label
								text={`✨ Experience: ${gameState.experience}/${gameState.experienceToNextLevel}`}
								fontSize={12}
							/>

							<ProgressBar
								progress={experiencePercentage / 100}
								barColor={QuickUtils.safeColor(COLORS.primary)}
								backgroundColor={QuickUtils.safeColor(COLORS.bg.secondary)}
								size={new UDim2(1, 0, 0, 20)}
							/>
						</VerticalFrame>
					</VerticalFrame>
				)}

				{/* Battle Info Section */}
				{gameState.isGameActive && (
					<VerticalFrame spacing={8}>
						<Label text="⚔️ Battle Info" fontSize={16} bold={true} />

						<Label
							text={`Wave: ${gameState.currentWave} | Enemies: ${gameState.enemiesRemaining}/${gameState.enemiesInWave}`}
							fontSize={14}
						/>

						{gameState.gamePhase === "betweenWaves" && (
							<Label
								text={`⏰ Next wave in ${math.ceil(gameState.timeUntilNextWave)} seconds`}
								fontSize={14}
								textColor={COLORS.primary}
							/>
						)}
					</VerticalFrame>
				)}

				{/* Abilities Section */}
				{gameState.isGameActive && (
					<VerticalFrame spacing={8}>
						<Label text="🔥 Unlocked Abilities" fontSize={16} bold={true} />

						<VerticalFrame spacing={4}>
							{gameState.abilitiesUnlocked.map((abilityId, index) => {
								const abilityNames: Record<string, string> = {
									QUAKE_PUNCH: "👊 Quake Punch",
									FIRE_FIST: "🔥 Fire Fist",
									ICE_AGE: "❄️ Ice Age",
									THREE_SWORD_STYLE: "⚔️ Three Sword Style",
									ROOM: "🔵 Room",
								};

								return (
									<Label
										key={index}
										text={abilityNames[abilityId] || abilityId}
										fontSize={12}
										textColor={COLORS.success}
									/>
								);
							})}
						</VerticalFrame>
					</VerticalFrame>
				)}

				{/* Game Controls */}
				<VerticalFrame spacing={8}>
					<Label text="🎮 Controls" fontSize={16} bold={true} />

					{!gameState.isGameActive ? (
						<>
							<Button
								text="⚔️ Start Battle"
								variant="success"
								onClick={handleStartGame}
								size={new UDim2(1, 0, 0, 40)}
							/>

							<Label
								text="💡 Use abilities (1-5 keys) to defeat waves of enemies! Level up to unlock new abilities!"
								fontSize={12}
								textColor={COLORS.text.secondary}
								textWrapped={true}
							/>
						</>
					) : (
						<>
							<Button
								text="🛑 Stop Battle"
								variant="error"
								onClick={handleStopGame}
								size={new UDim2(1, 0, 0, 40)}
							/>

							<Label
								text="⌨️ Press 1-5 to use abilities. Defeat all enemies to advance to the next wave!"
								fontSize={12}
								textColor={COLORS.text.secondary}
								textWrapped={true}
							/>
						</>
					)}

					{gameState.gamePhase === "ended" && gameState.playerHealth <= 0 && (
						<VerticalFrame spacing={8}>
							<Label text="💀 Game Over!" fontSize={18} textColor={COLORS.error} bold={true} />
							<Label
								text={`Final Score: ${gameState.score} | Waves: ${gameState.currentWave - 1} | Level: ${gameState.playerLevel}`}
								fontSize={14}
								textWrapped={true}
							/>
						</VerticalFrame>
					)}
				</VerticalFrame>
			</VerticalFrame>
		</Modal>
	);
}
