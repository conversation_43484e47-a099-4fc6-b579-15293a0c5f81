-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _design = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design")
local COLORS = _design.COLORS
local SIZES = _design.SIZES
local QuickUtils = TS.import(script, game:GetService("ReplicatedStorage"), "core", "helper", "GameUtils").QuickUtils
local Modal = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "modal", "Modal").Modal
local Button = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "button").Button
local IconButton = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "button").IconButton
local ListItemButton = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "button").ListItemButton
local Label = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "label", "Label").Label
local _frame = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "frame")
local ContainerFrame = _frame.ContainerFrame
local VerticalFrame = _frame.VerticalFrame
local HorizontalFrame = _frame.HorizontalFrame
local ScrollingFrame = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "frame").ScrollingFrame
local Slider = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "slider").Slider
local TextInput = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "input", "Input").Input
local function ComponentShowcase(props)
	local sliderValue, setSliderValue = React.useState(0.5)
	local textValue, setTextValue = React.useState("Sample text")
	local counter, setCounter = React.useState(0)
	if not props.isOpen then
		return React.createElement(React.Fragment)
	end
	return React.createElement(Modal, {
		title = "🎨 Complete GUI Component Showcase",
		isOpen = props.isOpen,
		onClose = props.onClose,
		width = 700,
		height = 600,
	}, React.createElement(ScrollingFrame, {
		size = UDim2.new(1, 0, 1, 0),
		backgroundTransparency = 1,
		borderThickness = 0,
		scrollingDirection = Enum.ScrollingDirection.Y,
		automaticCanvasSize = Enum.AutomaticSize.Y,
	}, React.createElement(VerticalFrame, {
		spacing = 32,
		padding = 16,
	}, React.createElement(VerticalFrame, {
		spacing = 16,
		padding = 0,
	}, React.createElement(Label, {
		text = "🔘 Buttons",
		fontSize = 18,
		bold = true,
	}), React.createElement(HorizontalFrame, {
		spacing = 16,
		padding = 0,
	}, React.createElement(Button, {
		text = "Primary Button",
		onClick = function()
			print("Primary button clicked!")
			setCounter(counter + 1)
		end,
		LayoutOrder = 1,
	}), React.createElement(Button, {
		text = "Secondary",
		variant = "secondary",
		onClick = function()
			return print("Secondary button clicked!")
		end,
		LayoutOrder = 2,
	}), React.createElement(Button, {
		text = "Danger",
		variant = "error",
		onClick = function()
			return print("Danger button clicked!")
		end,
		LayoutOrder = 3,
	})), React.createElement(HorizontalFrame, {
		spacing = 16,
		padding = 0,
	}, React.createElement(IconButton, {
		icon = "⚙️",
		onClick = function()
			return print("Settings icon clicked!")
		end,
		layoutOrder = 1,
	}), React.createElement(IconButton, {
		icon = "❤️",
		onClick = function()
			return print("Heart icon clicked!")
		end,
		layoutOrder = 2,
	}), React.createElement(IconButton, {
		icon = "🔍",
		onClick = function()
			return print("Search icon clicked!")
		end,
		layoutOrder = 3,
	})), React.createElement(VerticalFrame, {
		spacing = 4,
		padding = 0,
	}, React.createElement(Label, {
		text = "List Item Buttons:",
		fontSize = SIZES.fontSize.md,
		bold = true,
	}), React.createElement(ListItemButton, {
		onClick = function()
			return print("Profile clicked!")
		end,
	}, React.createElement(HorizontalFrame, {
		spacing = 8,
		padding = 8,
	}, React.createElement(Label, {
		text = "👤",
		fontSize = SIZES.fontSize.md,
		layoutOrder = 1,
	}), React.createElement(Label, {
		text = "Profile Settings",
		fontSize = SIZES.fontSize.md,
		layoutOrder = 2,
	}))), React.createElement(ListItemButton, {
		onClick = function()
			return print("Notifications clicked!")
		end,
	}, React.createElement(HorizontalFrame, {
		spacing = 8,
		padding = 8,
	}, React.createElement(Label, {
		text = "🔔",
		fontSize = SIZES.fontSize.md,
		layoutOrder = 1,
	}), React.createElement(Label, {
		text = "Notifications",
		fontSize = SIZES.fontSize.md,
		layoutOrder = 2,
	}))), React.createElement(ListItemButton, {
		onClick = function()
			return print("Privacy clicked!")
		end,
	}, React.createElement(HorizontalFrame, {
		spacing = 8,
		padding = 8,
	}, React.createElement(Label, {
		text = "🔒",
		fontSize = SIZES.fontSize.md,
		layoutOrder = 1,
	}), React.createElement(Label, {
		text = "Privacy & Security",
		fontSize = SIZES.fontSize.md,
		layoutOrder = 2,
	}))))), React.createElement(VerticalFrame, {
		spacing = 16,
		padding = 0,
	}, React.createElement(Label, {
		text = "📝 Labels & Text",
		fontSize = 18,
		bold = true,
	}), React.createElement(Label, {
		text = "This is a normal label",
		fontSize = SIZES.fontSize.md,
	}), React.createElement(Label, {
		text = "This is a bold label",
		fontSize = SIZES.fontSize.md,
		bold = true,
	}), React.createElement(Label, {
		text = "This is a large label",
		fontSize = 18,
	}), React.createElement(Label, {
		text = "This is a colored label",
		fontSize = SIZES.fontSize.md,
		textColor = COLORS.primary,
	}), React.createElement(Label, {
		text = "This is a wrapped label with a lot of text that should wrap to multiple lines when it gets too long for the container width.",
		fontSize = SIZES.fontSize.md,
		textWrapped = true,
	})), React.createElement(VerticalFrame, {
		spacing = 16,
		padding = 0,
	}, React.createElement(Label, {
		text = "📝 Text Input",
		fontSize = 18,
		bold = true,
	}), React.createElement(TextInput, {
		placeholder = "Enter some text...",
		value = textValue,
		onChange = function(newValue)
			return setTextValue(newValue)
		end,
		size = UDim2.new(1, 0, 0, 36),
	}), React.createElement(Label, {
		text = `Current input: "{textValue}"`,
		fontSize = 12,
		textColor = COLORS.text.secondary,
	})), React.createElement(VerticalFrame, {
		spacing = 16,
		padding = 0,
	}, React.createElement(Label, {
		text = "🎚️ Sliders",
		fontSize = 18,
		bold = true,
	}), React.createElement(Slider, {
		value = sliderValue,
		onChange = function(newValue)
			return setSliderValue(newValue)
		end,
		min = 0,
		max = 1,
		step = 0.01,
		size = UDim2.new(1, 0, 0, 30),
	}), React.createElement(Label, {
		text = `Slider value: {math.floor(sliderValue * 100)}%`,
		fontSize = 12,
		textColor = COLORS.text.secondary,
	})), React.createElement(VerticalFrame, {
		spacing = 16,
		padding = 0,
	}, React.createElement(Label, {
		text = "📦 Frames & Containers",
		fontSize = 18,
		bold = true,
	}), React.createElement(ContainerFrame, {
		backgroundColor = COLORS.bg.secondary,
		backgroundTransparency = 0,
		size = UDim2.new(1, 0, 0, 80),
		padding = 16,
		borderThickness = 1,
	}, React.createElement(Label, {
		text = "This is inside a ContainerFrame",
		fontSize = SIZES.fontSize.md,
	})), React.createElement(HorizontalFrame, {
		backgroundColor = COLORS.bg.surface,
		backgroundTransparency = 0,
		size = UDim2.new(1, 0, 0, 60),
		spacing = 16,
		padding = 16,
	}, React.createElement(Label, {
		text = "Horizontal",
		layoutOrder = 1,
	}), React.createElement(Label, {
		text = "Layout",
		layoutOrder = 2,
	}), React.createElement(Label, {
		text = "Frame",
		layoutOrder = 3,
	})), React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 12,
	}, React.createElement(Label, {
		text = "Grid-like Layout:",
		fontSize = SIZES.fontSize.md,
		bold = true,
	}), React.createElement(HorizontalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(ContainerFrame, {
		backgroundColor = COLORS.bg.surface,
		backgroundTransparency = 0,
		size = UDim2.new(0, 80, 0, 60),
		padding = 8,
		borderThickness = 1,
		layoutOrder = 1,
	}, React.createElement(Label, {
		text = "Item 1",
		fontSize = 12,
		alignment = Enum.TextXAlignment.Center,
	})), React.createElement(ContainerFrame, {
		backgroundColor = COLORS.bg.surface,
		backgroundTransparency = 0,
		size = UDim2.new(0, 80, 0, 60),
		padding = 8,
		borderThickness = 1,
		layoutOrder = 2,
	}, React.createElement(Label, {
		text = "Item 2",
		fontSize = 12,
		alignment = Enum.TextXAlignment.Center,
	})), React.createElement(ContainerFrame, {
		backgroundColor = COLORS.bg.surface,
		backgroundTransparency = 0,
		size = UDim2.new(0, 80, 0, 60),
		padding = 8,
		borderThickness = 1,
		layoutOrder = 3,
	}, React.createElement(Label, {
		text = "Item 3",
		fontSize = 12,
		alignment = Enum.TextXAlignment.Center,
	}))))), React.createElement(VerticalFrame, {
		spacing = 16,
		padding = 0,
	}, React.createElement(Label, {
		text = "🔢 Interactive Counter",
		fontSize = 18,
		bold = true,
	}), React.createElement(ContainerFrame, {
		backgroundColor = COLORS.bg.surface,
		backgroundTransparency = 0,
		size = UDim2.new(1, 0, 0, 100),
		padding = 16,
		borderThickness = 1,
	}, React.createElement(VerticalFrame, {
		spacing = 16,
		padding = 0,
	}, React.createElement(Label, {
		text = `Button clicks: {counter}`,
		fontSize = 18,
		alignment = Enum.TextXAlignment.Center,
		bold = true,
	}), React.createElement(HorizontalFrame, {
		spacing = 16,
		padding = 0,
		horizontalAlignment = Enum.HorizontalAlignment.Center,
	}, React.createElement(Button, {
		text = "+",
		onClick = function()
			return setCounter(counter + 1)
		end,
		size = UDim2.new(0, 40, 0, 30),
		LayoutOrder = 1,
	}), React.createElement(Button, {
		text = "Reset",
		variant = "secondary",
		onClick = function()
			return setCounter(0)
		end,
		LayoutOrder = 2,
	}), React.createElement(Button, {
		text = "-",
		variant = "error",
		onClick = function()
			return setCounter(math.max(0, counter - 1))
		end,
		size = UDim2.new(0, 40, 0, 30),
		LayoutOrder = 3,
	}))))), React.createElement(VerticalFrame, {
		spacing = 16,
		padding = 0,
	}, React.createElement(Label, {
		text = "🖼️ Images & Icons",
		fontSize = 18,
		bold = true,
	}), React.createElement(HorizontalFrame, {
		spacing = 16,
		padding = 0,
	}, React.createElement("frame", {
		Size = UDim2.new(0, 64, 0, 64),
		BackgroundColor3 = QuickUtils.safeColor(COLORS.bg.surface),
		BorderSizePixel = 1,
		BorderColor3 = QuickUtils.safeColor(COLORS.border.base),
		LayoutOrder = 1,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, 8),
	}), React.createElement(Label, {
		text = "🖼️",
		fontSize = 32,
		alignment = Enum.TextXAlignment.Center,
		position = UDim2.new(0.5, 0, 0.5, 0),
		anchorPoint = Vector2.new(0.5, 0.5),
	})), React.createElement("frame", {
		Size = UDim2.new(0, 64, 0, 64),
		BackgroundColor3 = QuickUtils.safeColor(COLORS.primary),
		BorderSizePixel = 0,
		LayoutOrder = 2,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, 32),
	}), React.createElement(Label, {
		text = "⭐",
		fontSize = 24,
		alignment = Enum.TextXAlignment.Center,
		position = UDim2.new(0.5, 0, 0.5, 0),
		anchorPoint = Vector2.new(0.5, 0.5),
		textColor = "#FFFFFF",
	})))), React.createElement(VerticalFrame, {
		spacing = 16,
		padding = 0,
	}, React.createElement(Label, {
		text = "🎨 Color Palette",
		fontSize = 18,
		bold = true,
	}), React.createElement(HorizontalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement("frame", {
		Size = UDim2.new(0, 40, 0, 40),
		BackgroundColor3 = QuickUtils.safeColor(COLORS.primary),
		BorderSizePixel = 0,
		LayoutOrder = 1,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, 4),
	})), React.createElement("frame", {
		Size = UDim2.new(0, 40, 0, 40),
		BackgroundColor3 = QuickUtils.safeColor(COLORS.success),
		BorderSizePixel = 0,
		LayoutOrder = 2,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, 4),
	})), React.createElement("frame", {
		Size = UDim2.new(0, 40, 0, 40),
		BackgroundColor3 = QuickUtils.safeColor(COLORS.warning),
		BorderSizePixel = 0,
		LayoutOrder = 3,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, 4),
	})), React.createElement("frame", {
		Size = UDim2.new(0, 40, 0, 40),
		BackgroundColor3 = QuickUtils.safeColor(COLORS.error),
		BorderSizePixel = 0,
		LayoutOrder = 4,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, 4),
	}))), React.createElement(Label, {
		text = "Primary • Success • Warning • Error",
		fontSize = 12,
		textColor = COLORS.text.secondary,
		alignment = Enum.TextXAlignment.Center,
	})), React.createElement(VerticalFrame, {
		spacing = 16,
		padding = 0,
	}, React.createElement(Label, {
		text = "📖 Component Documentation",
		fontSize = 18,
		bold = true,
	}), React.createElement(ContainerFrame, {
		backgroundColor = COLORS.bg.secondary,
		backgroundTransparency = 0,
		size = UDim2.new(1, 0, 0, 240),
		padding = 16,
		borderThickness = 1,
	}, React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(Label, {
		text = "Available Components:",
		fontSize = SIZES.fontSize.md,
		bold = true,
	}), React.createElement(Label, {
		text = "• Button, IconButton, ListItemButton - Interactive buttons with variants",
		fontSize = 12,
	}), React.createElement(Label, {
		text = "• Label - Text display with customization options",
		fontSize = 12,
	}), React.createElement(Label, {
		text = "• TextInput - Text input field with validation",
		fontSize = 12,
	}), React.createElement(Label, {
		text = "• DropdownButton - Dropdown selection with options",
		fontSize = 12,
	}), React.createElement(Label, {
		text = "• Slider - Value selection with min/max/step",
		fontSize = 12,
	}), React.createElement(Label, {
		text = "• LoadingIndicator - Animated loading spinner",
		fontSize = 12,
	}), React.createElement(Label, {
		text = "• Image - Image display with transparency support",
		fontSize = 12,
	}), React.createElement(Label, {
		text = "• ListView - Selectable list of items",
		fontSize = 12,
	}), React.createElement(Label, {
		text = "• Grid - Multi-column grid layout",
		fontSize = 12,
	}), React.createElement(Label, {
		text = "• ContainerFrame, VerticalFrame, HorizontalFrame - Layout containers",
		fontSize = 12,
	}), React.createElement(Label, {
		text = "• Modal - Overlay dialog component (streamlined)",
		fontSize = 12,
	}), React.createElement(Label, {
		text = "• ScrollingFrame - Scrollable content container",
		fontSize = 12,
	}), React.createElement(Label, {
		text = "• Toast - Notification system",
		fontSize = 12,
	}), React.createElement(Label, {
		text = "• ActionBar, AbilitySlot - Game-specific UI components",
		fontSize = 12,
	}), React.createElement(Label, {
		text = "• AutoDockFrame - Responsive layout component",
		fontSize = 12,
	}), React.createElement(Label, {
		text = "• FormField - Form input wrapper with validation",
		fontSize = 12,
	}), React.createElement(Label, {
		text = "• SplashScreen - Loading/intro screen component",
		fontSize = 12,
	})))), React.createElement(VerticalFrame, {
		spacing = 16,
		padding = 0,
	}, React.createElement(Label, {
		text = "💡 Usage Notes",
		fontSize = 18,
		bold = true,
	}), React.createElement(ContainerFrame, {
		backgroundColor = COLORS.bg.secondary,
		backgroundTransparency = 0,
		size = UDim2.new(1, 0, 0, 120),
		padding = 16,
		borderThickness = 1,
	}, React.createElement(VerticalFrame, {
		spacing = 8,
		padding = 0,
	}, React.createElement(Label, {
		text = "✅ Modal now has streamlined design with minimal spacing",
		fontSize = 12,
		textColor = COLORS.success,
	}), React.createElement(Label, {
		text = "✅ All weather effects use invisible parts (no more sky cubes)",
		fontSize = 12,
		textColor = COLORS.success,
	}), React.createElement(Label, {
		text = "✅ Improved AI behaviors with smarter pathfinding",
		fontSize = 12,
		textColor = COLORS.success,
	}), React.createElement(Label, {
		text = "✅ Enhanced weather sounds with proper audio IDs",
		fontSize = 12,
		textColor = COLORS.success,
	}), React.createElement(Label, {
		text = "✅ Complete component showcase with all available elements",
		fontSize = 12,
		textColor = COLORS.success,
	}), React.createElement(Label, {
		text = "🎯 All components follow consistent design patterns",
		fontSize = 12,
		textColor = COLORS.text.secondary,
	})))))))
end
return {
	ComponentShowcase = ComponentShowcase,
}
