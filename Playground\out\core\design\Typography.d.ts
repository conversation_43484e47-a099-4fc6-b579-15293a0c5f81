export declare const TYPOGRAPHY: {
    font: {
        primary: Enum.Font.Gotham;
        secondary: Enum.Font.GothamBold;
        heading: Enum.Font.FredokaOne;
        body: Enum.Font.Gotham;
        ui: Enum.Font.Gotham;
        mono: Enum.Font.RobotoMono;
    };
    weight: {
        regular: Enum.FontWeight.Regular;
        medium: Enum.FontWeight.Medium;
        semibold: Enum.FontWeight.SemiBold;
        bold: Enum.FontWeight.Bold;
        extrabold: Enum.FontWeight.ExtraBold;
    };
    fontFamily: {
        primary: Enum.Font.Gotham;
        display: Enum.Font.FredokaOne;
        playful: Enum.Font.Cartoon;
        readable: Enum.Font.SourceSans;
        technical: Enum.Font.RobotoMono;
    };
    size: {
        xs: number;
        sm: number;
        base: number;
        lg: number;
        xl: number;
        "2xl": number;
        "3xl": number;
        "4xl": number;
        "5xl": number;
    };
    lineHeight: {
        tight: number;
        normal: number;
        relaxed: number;
        loose: number;
    };
    letterSpacing: {
        tight: number;
        normal: number;
        wide: number;
        wider: number;
    };
};
