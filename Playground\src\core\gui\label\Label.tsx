import * as React from "@rbxts/react";
import { COLORS, TYPOGRAPHY, SIZES } from "../../design";
import { QuickUtils } from "../../helper/GameUtils";

interface LabelProps {
	text: string;
	textColor?: string;
	fontSize?: number;
	alignment?: Enum.TextXAlignment;
	verticalAlignment?: Enum.TextYAlignment;
	size?: UDim2;
	position?: UDim2;
	anchorPoint?: Vector2;
	layoutOrder?: number;
	bold?: boolean;
	autoSize?: boolean;
	textWrapped?: boolean;
	backgroundColor?: string;
	backgroundTransparency?: number;
	padding?: number;
	zIndex?: number;
}

export function Label(props: LabelProps): React.ReactElement {
	const textColor = props.textColor ?? COLORS.text.main;
	const fontSize = props.fontSize ?? SIZES.fontSize.md;
	const alignment = props.alignment ?? Enum.TextXAlignment.Left;
	const verticalAlignment = props.verticalAlignment ?? Enum.TextYAlignment.Center;
	const textWrapped = props.textWrapped ?? false;
	const backgroundColor = props.backgroundColor ?? "transparent";
	const backgroundTransparency = props.backgroundTransparency ?? 1;

	// Enhanced sizing logic
	const useAutoSize = props.autoSize ?? props.size === undefined;
	let defaultSize: UDim2;
	
	if (useAutoSize) {
		if (textWrapped) {
			defaultSize = new UDim2(1, 0, 0, 0); // Full width, auto height for wrapped text
		} else {
			defaultSize = new UDim2(0, 0, 0, 0); // Auto width and height
		}
	} else {
		defaultSize = new UDim2(1, 0, 0, fontSize + SIZES.padding.sm); // Fallback size
	}

	const size = props.size ?? defaultSize;

	// Font selection based on bold prop
	const font = props.bold ? TYPOGRAPHY.font.secondary : TYPOGRAPHY.font.primary;
	const fontWeight = props.bold ? TYPOGRAPHY.weight.bold : TYPOGRAPHY.weight.regular;

	return (
		<textlabel
			Text={props.text || ""} // Ensure text is never undefined
			TextColor3={QuickUtils.safeColor(textColor)}
			Font={font}
			// Note: FontWeight is handled by font selection
			TextSize={fontSize}
			Size={size}
			Position={props.position ?? new UDim2(0, 0, 0, 0)}
			AnchorPoint={props.anchorPoint ?? new Vector2(0, 0)}
			LayoutOrder={props.layoutOrder ?? 0}
			BackgroundColor3={QuickUtils.safeColor(backgroundColor, "#FFFFFF")}
			BackgroundTransparency={backgroundTransparency}
			BorderSizePixel={0}
			TextXAlignment={alignment}
			TextYAlignment={verticalAlignment}
			TextWrapped={textWrapped}
			TextTruncate={textWrapped ? Enum.TextTruncate.None : Enum.TextTruncate.AtEnd}
			AutomaticSize={useAutoSize ? Enum.AutomaticSize.XY : Enum.AutomaticSize.None}
			ZIndex={props.zIndex}
		>
			{/* Optional padding */}
			{props.padding && props.padding > 0 && (
				<uipadding
					PaddingLeft={new UDim(0, props.padding)}
					PaddingRight={new UDim(0, props.padding)}
					PaddingTop={new UDim(0, props.padding / 2)}
					PaddingBottom={new UDim(0, props.padding / 2)}
				/>
			)}
		</textlabel>
	);
}
