import * as React from "@rbxts/react";
import { COLORS, SIZES, TYPOGRAPHY, BORDER_RADIUS } from "../../design";
import { ZIndexManager } from "../layout/ZIndexManager";
import { Label } from "../label/Label";
import { ContainerFrame, VerticalFrame } from "../frame";
import { Overlay } from "../overlay/Overlay";
import { QuickUtils } from "../../helper/GameUtils";

interface SplashScreenProps {
	isVisible: boolean;
	loadingProgress: number; // 0 to 1
	loadingText: string;
	onLoadingComplete?: () => void;
}

export function SplashScreen(props: SplashScreenProps) {
	const [fadeOut, setFadeOut] = React.useState(false);
	const splashZIndex = ZIndexManager.getCurrentZIndex() + 1000; // Ensure highest z-index

	// Handle loading completion
	React.useEffect(() => {
		if (props.loadingProgress >= 1 && props.isVisible) {
			// Start fade out animation
			setFadeOut(true);

			// Complete loading after fade animation
			task.delay(1, () => {
				if (props.onLoadingComplete) {
					props.onLoadingComplete();
				}
			});
		}
	}, [props.loadingProgress, props.isVisible, props.onLoadingComplete]);

	// Reset fadeOut state when splash becomes visible again
	React.useEffect(() => {
		if (props.isVisible) {
			setFadeOut(false);
		}
	}, [props.isVisible]);

	// Don't render anything if splash is not visible
	if (!props.isVisible) {
		return <></>;
	}

	const backgroundTransparency = fadeOut ? 1 : 0;
	const contentTransparency = fadeOut ? 1 : 0;

	return (
		<Overlay
			backgroundColor={Color3.fromHex(COLORS.bg.base)}
			backgroundTransparency={backgroundTransparency}
			zIndex={splashZIndex}
			fullScreen={true}
			onBackdropClick={fadeOut ? props.onLoadingComplete : undefined} // Allow clicking through when fading out
		>
			{/* Main splash content */}
			<ContainerFrame
				backgroundColor={COLORS.bg.base}
				backgroundTransparency={0}
				size={new UDim2(1, 0, 1, 0)}
				padding={0}
				borderThickness={0}
				autoSize={Enum.AutomaticSize.None}
			>
				{/* Center content */}
				<VerticalFrame
					backgroundColor={COLORS.bg.base}
					backgroundTransparency={1}
					size={new UDim2(0.6, 0, 0.4, 0)}
					position={new UDim2(0.5, 0, 0.5, 0)}
					anchorPoint={new Vector2(0.5, 0.5)}
					spacing={48}
					horizontalAlignment={Enum.HorizontalAlignment.Center}
					padding={32}
				>
					{/* Logo/Title with enhanced styling */}
					<Label
						text="🎮 RoboxGames Core"
						fontSize={42}
						textColor={COLORS.primary}
						size={new UDim2(1, 0, 0, 60)}
						alignment={Enum.TextXAlignment.Center}
						bold={true}
						layoutOrder={1}
					/>

					{/* Subtitle with gradient effect */}
					<Label
						text="Advanced Game Framework"
						fontSize={18}
						textColor={COLORS.text.secondary}
						size={new UDim2(1, 0, 0, 30)}
						alignment={Enum.TextXAlignment.Center}
						layoutOrder={2}
					/>

					{/* Version badge */}
					<frame
						Size={new UDim2(0, 120, 0, 25)}
						Position={new UDim2(0.5, 0, 0, 0)}
						AnchorPoint={new Vector2(0.5, 0)}
						BackgroundColor3={Color3.fromHex(COLORS.badge.bg)}
						BorderSizePixel={0}
						LayoutOrder={2.5}
					>
						<uicorner CornerRadius={new UDim(0, BORDER_RADIUS.md)} />
						<uistroke Color={Color3.fromHex(COLORS.badge.border)} Thickness={1} />
						
						<textlabel
							Text="v3.0 Enterprise"
							Size={new UDim2(1, 0, 1, 0)}
							BackgroundTransparency={1}
							TextColor3={Color3.fromHex(COLORS.badge.text)}
							TextSize={12}
							Font={Enum.Font.Gotham}
							TextXAlignment={Enum.TextXAlignment.Center}
							TextYAlignment={Enum.TextYAlignment.Center}
						/>
					</frame>

					{/* Enhanced Loading progress bar container */}
					<ContainerFrame
						backgroundColor={COLORS.bg.secondary}
						backgroundTransparency={0}
						size={new UDim2(1, 0, 0, 12)}
						padding={0}
						borderThickness={1}
						autoSize={Enum.AutomaticSize.None}
						layoutOrder={3}
					>
						<uicorner CornerRadius={new UDim(0, BORDER_RADIUS.md)} />
						<uistroke Color={Color3.fromHex(COLORS.border.l1)} Thickness={1} />

						{/* Progress fill with gradient effect */}
						<frame
							BackgroundColor3={Color3.fromHex(COLORS.primary)}
							Size={new UDim2(props.loadingProgress, 0, 1, 0)}
							Position={new UDim2(0, 0, 0, 0)}
							BorderSizePixel={0}
						>
							<uicorner CornerRadius={new UDim(0, BORDER_RADIUS.md)} />
							
							{/* Progress shine effect */}
							<frame
								BackgroundColor3={Color3.fromHex("#FFFFFF")}
								BackgroundTransparency={0.8}
								Size={new UDim2(1, 0, 0.5, 0)}
								Position={new UDim2(0, 0, 0, 0)}
								BorderSizePixel={0}
							>
								<uicorner CornerRadius={new UDim(0, BORDER_RADIUS.sm)} />
							</frame>
						</frame>

						{/* Progress text overlay */}
						<textlabel
							Text={`${math.floor(props.loadingProgress * 100)}%`}
							Size={new UDim2(1, 0, 1, 0)}
							Position={new UDim2(0, 0, 0, 0)}
							BackgroundTransparency={1}
							TextColor3={QuickUtils.safeColor(COLORS.text.main)}
							TextSize={12}
							Font={Enum.Font.Gotham}
							TextXAlignment={Enum.TextXAlignment.Center}
							TextYAlignment={Enum.TextYAlignment.Center}
							TextStrokeTransparency={0.5}
							TextStrokeColor3={QuickUtils.safeColor(COLORS.bg.base)}
						/>
					</ContainerFrame>

					{/* Loading text */}
					<Label
						text={props.loadingText}
						fontSize={SIZES.fontSize.md}
						textColor={COLORS.text.main}
						size={new UDim2(1, 0, 0, 20)}
						alignment={Enum.TextXAlignment.Center}
						layoutOrder={4}
					/>
				</VerticalFrame>

				{/* Footer */}
				<Label
					text="Powered by @roboxgames/core framework"
					fontSize={12}
					textColor={COLORS.text.secondary}
					size={new UDim2(1, 0, 0, 20)}
					position={new UDim2(0, 0, 1, -40)}
					alignment={Enum.TextXAlignment.Center}
				/>
			</ContainerFrame>
		</Overlay>
	);
}
