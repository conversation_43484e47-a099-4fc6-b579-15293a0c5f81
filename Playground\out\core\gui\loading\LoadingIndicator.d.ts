import * as React from "@rbxts/react";
interface LoadingSpinnerProps {
    size?: number;
    color?: string;
    speed?: number;
}
export declare const LoadingSpinner: React.MemoExoticComponent<(props: LoadingSpinnerProps) => JSX.Element>;
interface LoadingIndicatorProps {
    text?: string;
    size?: "sm" | "md" | "lg";
}
export declare function LoadingIndicator(props: LoadingIndicatorProps): JSX.Element;
export {};
