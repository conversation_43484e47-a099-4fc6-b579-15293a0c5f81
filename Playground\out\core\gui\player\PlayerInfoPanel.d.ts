import * as React from "@rbxts/react";
interface PlayerInfoPanelProps {
    player?: Player;
    position?: UDim2;
    anchorPoint?: Vector2;
    size?: UDim2;
    zIndex?: number;
    showAvatar?: boolean;
    showLevel?: boolean;
    showStats?: boolean;
    showRobux?: boolean;
    variant?: "primary" | "compact" | "detailed";
    responsive?: boolean;
}
export declare function PlayerInfoPanel(props: PlayerInfoPanelProps): React.ReactElement;
export {};
