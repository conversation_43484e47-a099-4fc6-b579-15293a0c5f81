-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _design = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design")
local COLORS = _design.COLORS
local SIZES = _design.SIZES
local BORDER_RADIUS = _design.BORDER_RADIUS
local ResponsiveManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "ResponsiveManager").ResponsiveManager
local function RobloxPanel(props)
	local responsiveManager = React.useMemo(function()
		local _exitType, _returns = TS.try(function()
			return TS.TRY_RETURN, { ResponsiveManager:getInstance() }
		end, function(error)
			warn(`❌ Failed to get ResponsiveManager: {error}`)
			return TS.TRY_RETURN, { nil }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end, {})
	-- Calculate responsive values
	local _condition = props.padding
	if _condition == nil then
		_condition = SIZES.panel.padding
	end
	local basePadding = _condition
	local padding = if props.responsiveMargin and responsiveManager then responsiveManager:getResponsiveMargin(basePadding) else basePadding
	local _condition_1 = props.headerHeight
	if _condition_1 == nil then
		_condition_1 = SIZES.panel.headerHeight
	end
	local headerHeight = _condition_1
	-- Get variant-based colors
	local getVariantColors = function()
		local variant = props.variant or "primary"
		repeat
			if variant == "primary" then
				return {
					background = COLORS.roblox.panel,
					backgroundTransparency = COLORS.transparency.panel,
					border = COLORS.roblox["panel-border"],
					borderTransparency = COLORS.transparency["panel-border"],
					shadow = COLORS.roblox["panel-shadow"],
					shadowTransparency = COLORS.transparency["panel-shadow"],
				}
			end
			if variant == "secondary" then
				return {
					background = COLORS.bg.surface,
					backgroundTransparency = 0,
					border = COLORS.border.panel,
					borderTransparency = 0.3,
					shadow = COLORS.shadow.panel,
					shadowTransparency = 0.6,
				}
			end
			if variant == "dark" then
				return {
					background = "#191919",
					backgroundTransparency = 0.1,
					border = "#3C3C3C",
					borderTransparency = 0.6,
					shadow = COLORS.shadow.xl,
					shadowTransparency = 0.2,
				}
			end
			if variant == "light" then
				return {
					background = "#414141",
					backgroundTransparency = 0.1,
					border = "#646464",
					borderTransparency = 0.6,
					shadow = COLORS.shadow.md,
					shadowTransparency = 0.5,
				}
			end
			return {
				background = COLORS.roblox.panel,
				backgroundTransparency = COLORS.transparency.panel,
				border = COLORS.roblox["panel-border"],
				borderTransparency = COLORS.transparency["panel-border"],
				shadow = COLORS.roblox["panel-shadow"],
				shadowTransparency = COLORS.transparency["panel-shadow"],
			}
		until true
	end
	local colors = getVariantColors()
	local _attributes = {
		BackgroundColor3 = Color3.fromHex(colors.background),
		BackgroundTransparency = colors.backgroundTransparency,
		Size = props.size or UDim2.new(0, 400, 0, 300),
		Position = props.position or UDim2.new(0.5, 0, 0.5, 0),
		AnchorPoint = props.anchorPoint or Vector2.new(0.5, 0.5),
		LayoutOrder = props.layoutOrder,
	}
	local _condition_2 = props.zIndex
	if _condition_2 == nil then
		_condition_2 = 5
	end
	_attributes.ZIndex = _condition_2
	_attributes.BorderSizePixel = 0
	_attributes.ClipsDescendants = true
	local _exp = React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.panel),
	})
	local _exp_1 = React.createElement("uistroke", {
		Color = Color3.fromHex(colors.border),
		Thickness = 1,
		Transparency = colors.borderTransparency,
	})
	local _exp_2 = props.shadow ~= false and (React.createElement("frame", {
		Size = UDim2.new(1, 4, 1, 4),
		Position = UDim2.new(0, 2, 0, 2),
		AnchorPoint = Vector2.new(0, 0),
		BackgroundColor3 = Color3.fromHex(colors.shadow),
		BackgroundTransparency = colors.shadowTransparency,
		BorderSizePixel = 0,
		ZIndex = -1,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.panel),
	})))
	local _condition_3 = props.title
	if _condition_3 ~= "" and _condition_3 then
		_condition_3 = (React.createElement("frame", {
			Size = UDim2.new(1, 0, 0, headerHeight),
			Position = UDim2.new(0, 0, 0, 0),
			BackgroundTransparency = 1,
			BorderSizePixel = 0,
			ZIndex = 1,
		}, React.createElement("textlabel", {
			Size = UDim2.new(1, if props.onClose then -40 else -padding, 1, 0),
			Position = UDim2.new(0, padding, 0, 0),
			BackgroundTransparency = 1,
			Text = props.title,
			TextColor3 = Color3.fromHex(COLORS.text.main),
			TextSize = 16,
			Font = Enum.Font.GothamBold,
			TextXAlignment = Enum.TextXAlignment.Left,
			TextYAlignment = Enum.TextYAlignment.Center,
			TextTruncate = Enum.TextTruncate.AtEnd,
			ZIndex = 2,
		}), props.onClose and (React.createElement("textbutton", {
			Size = UDim2.new(0, 32, 0, 32),
			Position = UDim2.new(1, -padding - 32, 0.5, 0),
			AnchorPoint = Vector2.new(0, 0.5),
			BackgroundColor3 = Color3.fromHex(COLORS.error),
			BackgroundTransparency = 0.1,
			BorderSizePixel = 0,
			Text = "✕",
			TextColor3 = Color3.fromHex(COLORS.text.main),
			TextSize = 14,
			Font = Enum.Font.GothamBold,
			ZIndex = 2,
			Event = {
				MouseButton1Click = props.onClose,
			},
		}, React.createElement("uicorner", {
			CornerRadius = UDim.new(0, BORDER_RADIUS.button),
		}))), React.createElement("frame", {
			Size = UDim2.new(1, -padding * 2, 0, 1),
			Position = UDim2.new(0, padding, 1, -1),
			BackgroundColor3 = Color3.fromHex(colors.border),
			BackgroundTransparency = 0.5,
			BorderSizePixel = 0,
			ZIndex = 1,
		})))
	end
	local _attributes_1 = {}
	local _value = props.title
	_attributes_1.Size = UDim2.new(1, 0, 1, if _value ~= "" and _value then -headerHeight else 0)
	local _value_1 = props.title
	_attributes_1.Position = UDim2.new(0, 0, 0, if _value_1 ~= "" and _value_1 then headerHeight else 0)
	_attributes_1.BackgroundTransparency = 1
	_attributes_1.BorderSizePixel = 0
	_attributes_1.ClipsDescendants = true
	_attributes_1.ZIndex = 1
	return React.createElement("frame", _attributes, _exp, _exp_1, _exp_2, _condition_3, React.createElement("frame", _attributes_1, React.createElement("uipadding", {
		PaddingTop = UDim.new(0, padding),
		PaddingBottom = UDim.new(0, padding),
		PaddingLeft = UDim.new(0, padding),
		PaddingRight = UDim.new(0, padding),
	}), props.children))
end
return {
	RobloxPanel = RobloxPanel,
}
