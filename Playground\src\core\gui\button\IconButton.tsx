import * as React from "@rbxts/react";
import { COLORS, SIZES, BORDER_RADIUS } from "../../design";

interface IconButtonProps {
	icon: string;
	onClick: () => void;
	disabled?: boolean;
	layoutOrder?: number;
	size?: UDim2;
	position?: UDim2;
	anchorPoint?: Vector2;
	backgroundColor?: string;
	iconColor?: Color3;
}

export function IconButton(props: IconButtonProps): React.ReactElement {
	const [hovered, setHovered] = React.useState(false);

	// Get the appropriate color based on state
	const bgColorHSL =
		props.backgroundColor ??
		(props.disabled ? COLORS.bg.secondary : hovered ? COLORS.bg["surface-hover"] : COLORS.bg.surface);

	const size = props.size ?? new UDim2(0, SIZES.button.height, 0, SIZES.button.height); // Square by default

	return (
		<textbutton
			Text=""
			BackgroundColor3={Color3.fromHex(bgColorHSL)}
			Size={size}
			Position={props.position}
			AnchorPoint={props.anchorPoint}
			LayoutOrder={props.layoutOrder}
			AutoButtonColor={false}
			Event={{
				Activated: props.onClick,
				MouseEnter: () => !props.disabled && setHovered(true),
				MouseLeave: () => setHovered(false),
			}}
			BorderSizePixel={0}
		>
			{/* Rounded corners */}
			<uicorner CornerRadius={new UDim(0, BORDER_RADIUS.md)} />

			{/* Border using border.l2 */}
			<uistroke Color={Color3.fromHex(COLORS.border.l2)} Thickness={1} Transparency={props.disabled ? 0.5 : 0} />

			{/* Icon */}
			{string.find(props.icon, "rbxassetid://")[0] !== undefined ? (
				<imagelabel
					Image={props.icon}
					ImageColor3={
						props.iconColor ?? Color3.fromHex(props.disabled ? COLORS.text.secondary : COLORS.text.main)
					}
					Size={new UDim2(0.7, 0, 0.7, 0)}
					Position={new UDim2(0.5, 0, 0.5, 0)}
					AnchorPoint={new Vector2(0.5, 0.5)}
					BackgroundTransparency={1}
					ScaleType={Enum.ScaleType.Fit}
				/>
			) : (
				<textlabel
					Text={props.icon}
					TextColor3={
						props.iconColor ?? Color3.fromHex(props.disabled ? COLORS.text.secondary : COLORS.text.main)
					}
					Font={Enum.Font.SourceSansBold}
					TextSize={18}
					Size={new UDim2(1, 0, 1, 0)}
					BackgroundTransparency={1}
					TextXAlignment={Enum.TextXAlignment.Center}
					TextYAlignment={Enum.TextYAlignment.Center}
					TextScaled={false}
				/>
			)}
		</textbutton>
	);
}
