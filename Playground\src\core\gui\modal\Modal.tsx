import * as React from "@rbxts/react";
import { COLORS, SIZES, BORDER_RADIUS, TYPOGRAPHY } from "../../design";
import { IconButton } from "../button";
import { Label } from "../label/Label";
import { ContainerFrame } from "../frame";
import { Overlay } from "../overlay/Overlay";
import { ErrorBoundary } from "../error-boundary";

interface ModalProps {
	title: string;
	isOpen: boolean;
	onClose: () => void;
	children?: React.ReactNode;
	width?: number;
	height?: number;
}

export function Modal(props: ModalProps) {
	const [isAnimating, setIsAnimating] = React.useState(false);
	const width = props.width ?? SIZES.modal.medium.width;
	const height = props.height ?? SIZES.modal.medium.height;

	// Handle open/close animations
	React.useEffect(() => {
		if (props.isOpen) {
			setIsAnimating(true);
		}
	}, [props.isOpen]);

	const handleClose = () => {
		setIsAnimating(false);
		// Delay actual close to allow animation
		task.delay(0.2, () => {
			props.onClose();
		});
	};

	if (!props.isOpen && !isAnimating) return <></>;

	const modalScale = isAnimating && props.isOpen ? 1 : 0.9;
	const modalTransparency = isAnimating && props.isOpen ? 0 : 0.3;

	return (
		<Overlay onBackdropClick={handleClose} backgroundColor={Color3.fromRGB(0, 0, 0)}>
			{/* Modern modal container with enhanced styling */}
			<textbutton
				Text="" // Empty text to prevent React text rendering issues
				BackgroundColor3={Color3.fromHex(COLORS.bg.base)}
				Size={new UDim2(0, width, 0, height)}
				Position={new UDim2(0.5, 0, 0.5, 0)}
				AnchorPoint={new Vector2(0.5, 0.5)}
				ZIndex={12}
				AutoButtonColor={false}
				BorderSizePixel={0}
				BackgroundTransparency={modalTransparency}
				Event={{
					Activated: () => {
						// Stop propagation by doing nothing - this prevents backdrop click
					},
				}}
			>
				{/* Modern rounded corners */}
				<uicorner CornerRadius={new UDim(0, BORDER_RADIUS.modal)} />
				
				{/* Modern subtle border */}
				<uistroke Color={Color3.fromHex(COLORS.border.base)} Thickness={1} Transparency={0.5} />

				{/* Enhanced drop shadow */}
				<frame
					Size={new UDim2(1, 8, 1, 8)}
					Position={new UDim2(0, 4, 0, 4)}
					AnchorPoint={new Vector2(0, 0)}
					BackgroundColor3={Color3.fromRGB(0, 0, 0)}
					BackgroundTransparency={0.9}
					BorderSizePixel={0}
					ZIndex={-1}
				>
					<uicorner CornerRadius={new UDim(0, BORDER_RADIUS.modal)} />
				</frame>

				{/* Header section with title and close button */}
				<frame
					Size={new UDim2(1, 0, 0, 60)}
					Position={new UDim2(0, 0, 0, 0)}
					BackgroundColor3={Color3.fromHex(COLORS.bg.secondary)}
					BorderSizePixel={0}
				>
					<uicorner CornerRadius={new UDim(0, BORDER_RADIUS.modal)} />
					
					{/* Cover bottom corners */}
					<frame
						Size={new UDim2(1, 0, 0, BORDER_RADIUS.modal)}
						Position={new UDim2(0, 0, 1, -BORDER_RADIUS.modal)}
						BackgroundColor3={Color3.fromHex(COLORS.bg.secondary)}
						BorderSizePixel={0}
					/>

					<uipadding
						PaddingLeft={new UDim(0, SIZES.padding.lg)}
						PaddingRight={new UDim(0, SIZES.padding.lg)}
						PaddingTop={new UDim(0, SIZES.padding.md)}
						PaddingBottom={new UDim(0, SIZES.padding.md)}
					/>

					{/* Title label with proper text wrapping */}
					<textlabel
						Text={props.title}
						Size={new UDim2(1, -50, 1, 0)}
						Position={new UDim2(0, 0, 0, 0)}
						BackgroundTransparency={1}
						TextColor3={Color3.fromHex(COLORS.text.main)}
						Font={Enum.Font.Gotham}
						TextSize={SIZES.fontSize.xl}
						// Note: FontWeight is handled by font selection
						TextXAlignment={Enum.TextXAlignment.Left}
						TextYAlignment={Enum.TextYAlignment.Center}
						TextWrapped={false}
						TextTruncate={Enum.TextTruncate.AtEnd}
					/>

					{/* Modern close button */}
					<textbutton
						Text="✕"
						Size={new UDim2(0, 32, 0, 32)}
						Position={new UDim2(1, -40, 0.5, -16)}
						BackgroundColor3={Color3.fromHex(COLORS.bg.hover)}
						BackgroundTransparency={1}
						TextColor3={Color3.fromHex(COLORS.text.secondary)}
						Font={Enum.Font.Gotham}
						TextSize={SIZES.fontSize.lg}
						BorderSizePixel={0}
						AutoButtonColor={false}
						Event={{
							Activated: handleClose,
							MouseEnter: (rbx) => {
								rbx.BackgroundTransparency = 0;
								rbx.TextColor3 = Color3.fromHex(COLORS.text.main);
							},
							MouseLeave: (rbx) => {
								rbx.BackgroundTransparency = 1;
								rbx.TextColor3 = Color3.fromHex(COLORS.text.secondary);
							},
						}}
					>
						<uicorner CornerRadius={new UDim(0, BORDER_RADIUS.sm)} />
					</textbutton>
				</frame>

				{/* Content area with proper spacing */}
				<frame
					Size={new UDim2(1, 0, 1, -60)}
					Position={new UDim2(0, 0, 0, 60)}
					BackgroundTransparency={1}
					BorderSizePixel={0}
				>
					<uipadding
						PaddingLeft={new UDim(0, SIZES.padding.lg)}
						PaddingRight={new UDim(0, SIZES.padding.lg)}
						PaddingTop={new UDim(0, SIZES.padding.md)}
						PaddingBottom={new UDim(0, SIZES.padding.lg)}
					/>

					<ErrorBoundary children={props.children} />
				</frame>
			</textbutton>
		</Overlay>
	);
}
