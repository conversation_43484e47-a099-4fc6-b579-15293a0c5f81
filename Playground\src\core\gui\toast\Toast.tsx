import * as React from "@rbxts/react";
import { COLORS, SIZES, TYPOGRAPHY, BORDER_RADIUS } from "../../design";
import { ZIndexManager } from "../layout/ZIndexManager";

export interface ToastData {
	id: string;
	title: string;
	message?: string;
	type: "success" | "error" | "warning" | "info";
	duration?: number; // Auto-dismiss after duration (ms), 0 for manual dismiss
	action?: {
		label: string;
		onClick: () => void;
	};
}

interface ToastProps {
	toast: ToastData;
	onDismiss: (id: string) => void;
	index: number;
}

export function Toast(props: ToastProps): React.ReactElement {
	const [isVisible, setIsVisible] = React.useState(false);
	const [isRemoving, setIsRemoving] = React.useState(false);

	// Auto-dismiss logic
	React.useEffect(() => {
		// Animate in
		task.delay(0.1, () => setIsVisible(true));

		// Auto-dismiss if duration is set
		if (props.toast.duration !== undefined && props.toast.duration > 0) {
			task.delay(props.toast.duration / 1000, () => {
				handleDismiss();
			});
		}
	}, []);

	const handleDismiss = () => {
		setIsRemoving(true);
		setIsVisible(false);
		task.delay(0.3, () => {
			props.onDismiss(props.toast.id);
		});
	};

	const getTypeColors = () => {
		switch (props.toast.type) {
			case "success":
				return {
					background: COLORS.green,
					border: COLORS.border.base,
					icon: "✓",
					textColor: COLORS.text.main,
				};
			case "error":
				return {
					background: COLORS.error,
					border: COLORS.border.base,
					icon: "✕",
					textColor: COLORS.text.main,
				};
			case "warning":
				return {
					background: COLORS.yellow,
					border: COLORS.border.base,
					icon: "⚠",
					textColor: COLORS.text.inverse, // Dark text on yellow
				};
			case "info":
				return {
					background: COLORS["toast-info"],
					border: COLORS["toast-info"],
					icon: "ℹ",
					textColor: COLORS.text.inverse, // Dark text on light blue
				};
		}
	};

	const typeColors = getTypeColors();
	const yOffset = props.index * 80; // Stack toasts vertically
	const transparency = isVisible && !isRemoving ? 0 : 1;

	return (
		<frame
			Size={new UDim2(0, 300, 0, 70)}
			Position={new UDim2(1, -20, 0, 20 + yOffset)}
			AnchorPoint={new Vector2(1, 0)}
			BackgroundColor3={Color3.fromHex(typeColors.background)}
			BackgroundTransparency={transparency}
			BorderSizePixel={0}
			ZIndex={ZIndexManager.getCurrentZIndex() + 100}
		>
			<uicorner CornerRadius={new UDim(0, BORDER_RADIUS.lg)} />
			<uistroke Color={Color3.fromHex(typeColors.border)} Thickness={2} Transparency={transparency} />

			{/* Drop shadow effect */}
			<frame
				Size={new UDim2(1, 4, 1, 4)}
				Position={new UDim2(0, 2, 0, 2)}
				AnchorPoint={new Vector2(0, 0)}
				BackgroundColor3={Color3.fromRGB(0, 0, 0)}
				BackgroundTransparency={0.7}
				BorderSizePixel={0}
				ZIndex={-1}
			>
				<uicorner CornerRadius={new UDim(0, BORDER_RADIUS.lg)} />
			</frame>

			{/* Left accent bar */}
			<frame
				Size={new UDim2(0, 4, 1, 0)}
				Position={new UDim2(0, 0, 0, 0)}
				BackgroundColor3={Color3.fromHex(typeColors.border)}
				BackgroundTransparency={transparency}
				BorderSizePixel={0}
			>
				<uicorner CornerRadius={new UDim(0, BORDER_RADIUS.sm)} />
			</frame>

			{/* Content */}
			<frame Size={new UDim2(1, -50, 1, 0)} Position={new UDim2(0, 16, 0, 0)} BackgroundTransparency={1}>
				{/* Icon */}
				<textlabel
					Text={typeColors.icon}
					Size={new UDim2(0, 20, 0, 20)}
					Position={new UDim2(0, 0, 0, 8)}
					BackgroundTransparency={1}
					TextColor3={Color3.fromHex(typeColors.textColor)}
					TextSize={16}
					Font={Enum.Font.Gotham}
					TextXAlignment={Enum.TextXAlignment.Center}
					TextYAlignment={Enum.TextYAlignment.Center}
					TextTransparency={transparency}
				/>

				{/* Title */}
				<textlabel
					Text={props.toast.title}
					Size={new UDim2(1, -30, 0, 20)}
					Position={new UDim2(0, 30, 0, 8)}
					BackgroundTransparency={1}
					TextColor3={Color3.fromHex(typeColors.textColor)}
					TextSize={SIZES.fontSize.md}
					Font={Enum.Font.Gotham}
					TextXAlignment={Enum.TextXAlignment.Left}
					TextYAlignment={Enum.TextYAlignment.Center}
					TextTransparency={transparency}
					TextTruncate={Enum.TextTruncate.AtEnd}
				/>

				{/* Message */}
				{props.toast.message !== undefined && (
					<textlabel
						Text={props.toast.message}
						Size={new UDim2(1, -30, 0, 15)}
						Position={new UDim2(0, 30, 0, 30)}
						BackgroundTransparency={1}
						TextColor3={Color3.fromHex(typeColors.textColor)}
						TextSize={12}
						Font={Enum.Font.Gotham}
						TextXAlignment={Enum.TextXAlignment.Left}
						TextYAlignment={Enum.TextYAlignment.Center}
						TextTransparency={transparency}
						TextTruncate={Enum.TextTruncate.AtEnd}
					/>
				)}

				{/* Action button */}
				{props.toast.action && (
					<textbutton
						Text={props.toast.action.label}
						Size={new UDim2(0, 60, 0, 18)}
						Position={new UDim2(1, -70, 0, 48)}
						BackgroundColor3={Color3.fromHex(typeColors.background)}
						BackgroundTransparency={transparency}
						TextColor3={Color3.fromHex(COLORS.text.main)}
						TextSize={11}
						Font={Enum.Font.Gotham}
						AutoButtonColor={false}
						BorderSizePixel={0}
						Event={{
							Activated: () => {
								props.toast.action!.onClick();
								handleDismiss();
							},
						}}
					>
						<uicorner CornerRadius={new UDim(0, BORDER_RADIUS.sm)} />
					</textbutton>
				)}
			</frame>

			{/* Dismiss button */}
			<textbutton
				Text="×"
				Size={new UDim2(0, 20, 0, 20)}
				Position={new UDim2(1, -25, 0, 5)}
				BackgroundTransparency={1}
				TextColor3={Color3.fromHex(COLORS.text.secondary)}
				TextSize={18}
				Font={Enum.Font.Gotham}
				AutoButtonColor={false}
				BorderSizePixel={0}
				Event={{
					Activated: handleDismiss,
				}}
			/>
		</frame>
	);
}
