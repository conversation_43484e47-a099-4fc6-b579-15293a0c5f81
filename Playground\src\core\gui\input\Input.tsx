import * as React from "@rbxts/react";
import { COLORS, SIZES, TYPOGRAPHY, BORDER_RADIUS } from "../../design";

interface InputProps {
	placeholder?: string;
	onChange: (text: string) => void;
	value?: string;
	size?: UDim2;
	position?: UDim2;
	anchorPoint?: Vector2;
	layoutOrder?: number;
	disabled?: boolean;
	multiline?: boolean;
	clearTextOnFocus?: boolean;
	textColor?: string;
	backgroundColor?: string;
	borderColor?: string;
	placeholderColor?: string;
}

export function Input(props: InputProps) {
	const [text, setText] = React.useState(props.value ?? "");
	const textBoxRef = React.useRef<TextBox>();

	// Update internal state when value prop changes
	React.useEffect(() => {
		if (props.value !== undefined) {
			setText(props.value);
		}
	}, [props.value]);

	React.useEffect(() => {
		const tb = textBoxRef.current;
		if (tb) {
			const conn = tb.GetPropertyChangedSignal("Text").Connect(() => {
				const newText = tb.Text;
				setText(newText);
				props.onChange(newText);
			});

			// Return cleanup function to disconnect when component unmounts
			return () => conn.Disconnect();
		}
	}, []);

	// Note: TextBox selection styling properties are not available in Roblox
	// SelectionColor3 and SelectionTransparency are not valid properties for TextBox

	// Apply customizable properties with defaults
	const placeholder = props.placeholder ?? "Enter text...";
	const size = props.size ?? new UDim2(0, SIZES.input.width, 0, SIZES.input.height);
	const textColor = props.textColor ?? COLORS.text.main;
	const backgroundColor = props.backgroundColor ?? COLORS.bg.base;
	const borderColor = props.borderColor ?? COLORS.border.l2;
	const placeholderColor = props.placeholderColor ?? COLORS.text.secondary;
	const clearTextOnFocus = props.clearTextOnFocus ?? false;
	const multiline = props.multiline ?? false;

	return (
		<textbox
			ref={textBoxRef}
			PlaceholderText={placeholder}
			PlaceholderColor3={Color3.fromHex(placeholderColor)}
			Text={text}
			TextColor3={Color3.fromHex(textColor)}
			BackgroundColor3={Color3.fromHex(backgroundColor)}
			Size={size}
			Position={props.position}
			AnchorPoint={props.anchorPoint}
			LayoutOrder={props.layoutOrder}
			Font={Enum.Font.Gotham}
			TextSize={SIZES.fontSize.md}
			ClearTextOnFocus={clearTextOnFocus}
			TextWrapped={multiline}
			MultiLine={multiline}
			BorderSizePixel={0}
			Active={!props.disabled}
			TextEditable={!props.disabled}
		>
			{/* Rounded corners */}
			<uicorner CornerRadius={new UDim(0, BORDER_RADIUS.md)} />

			{/* Border using customizable border color */}
			<uistroke Color={Color3.fromHex(borderColor)} Thickness={1} Transparency={props.disabled ? 0.7 : 0} />
		</textbox>
	);
}
