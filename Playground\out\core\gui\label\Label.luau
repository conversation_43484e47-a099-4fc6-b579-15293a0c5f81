-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _design = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design")
local COLORS = _design.COLORS
local TYPOGRAPHY = _design.TYPOGRAPHY
local SIZES = _design.SIZES
local QuickUtils = TS.import(script, game:GetService("ReplicatedStorage"), "core", "helper", "GameUtils").QuickUtils
local function Label(props)
	local _condition = props.textColor
	if _condition == nil then
		_condition = COLORS.text.main
	end
	local textColor = _condition
	local _condition_1 = props.fontSize
	if _condition_1 == nil then
		_condition_1 = SIZES.fontSize.md
	end
	local fontSize = _condition_1
	local alignment = props.alignment or Enum.TextXAlignment.Left
	local verticalAlignment = props.verticalAlignment or Enum.TextYAlignment.Center
	local _condition_2 = props.textWrapped
	if _condition_2 == nil then
		_condition_2 = false
	end
	local textWrapped = _condition_2
	local _condition_3 = props.backgroundColor
	if _condition_3 == nil then
		_condition_3 = "transparent"
	end
	local backgroundColor = _condition_3
	local _condition_4 = props.backgroundTransparency
	if _condition_4 == nil then
		_condition_4 = 1
	end
	local backgroundTransparency = _condition_4
	-- Enhanced sizing logic
	local _condition_5 = props.autoSize
	if _condition_5 == nil then
		_condition_5 = props.size == nil
	end
	local useAutoSize = _condition_5
	local defaultSize
	if useAutoSize then
		if textWrapped then
			defaultSize = UDim2.new(1, 0, 0, 0)
		else
			defaultSize = UDim2.new(0, 0, 0, 0)
		end
	else
		defaultSize = UDim2.new(1, 0, 0, fontSize + SIZES.padding.sm)
	end
	local size = props.size or defaultSize
	-- Font selection based on bold prop
	local font = if props.bold then TYPOGRAPHY.font.secondary else TYPOGRAPHY.font.primary
	local fontWeight = if props.bold then TYPOGRAPHY.weight.bold else TYPOGRAPHY.weight.regular
	local _attributes = {}
	local _condition_6 = props.text
	if not (_condition_6 ~= "" and _condition_6) then
		_condition_6 = ""
	end
	_attributes.Text = _condition_6
	_attributes.TextColor3 = QuickUtils.safeColor(textColor)
	_attributes.Font = font
	_attributes.TextSize = fontSize
	_attributes.Size = size
	_attributes.Position = props.position or UDim2.new(0, 0, 0, 0)
	_attributes.AnchorPoint = props.anchorPoint or Vector2.new(0, 0)
	local _condition_7 = props.layoutOrder
	if _condition_7 == nil then
		_condition_7 = 0
	end
	_attributes.LayoutOrder = _condition_7
	_attributes.BackgroundColor3 = QuickUtils.safeColor(backgroundColor, "#FFFFFF")
	_attributes.BackgroundTransparency = backgroundTransparency
	_attributes.BorderSizePixel = 0
	_attributes.TextXAlignment = alignment
	_attributes.TextYAlignment = verticalAlignment
	_attributes.TextWrapped = textWrapped
	_attributes.TextTruncate = if textWrapped then Enum.TextTruncate.None else Enum.TextTruncate.AtEnd
	_attributes.AutomaticSize = if useAutoSize then Enum.AutomaticSize.XY else Enum.AutomaticSize.None
	_attributes.ZIndex = props.zIndex
	local _condition_8 = props.padding
	if _condition_8 ~= 0 and _condition_8 == _condition_8 and _condition_8 then
		_condition_8 = props.padding > 0
	end
	return React.createElement("textlabel", _attributes, if _condition_8 ~= 0 and _condition_8 == _condition_8 and _condition_8 then (React.createElement("uipadding", {
		PaddingLeft = UDim.new(0, props.padding),
		PaddingRight = UDim.new(0, props.padding),
		PaddingTop = UDim.new(0, props.padding / 2),
		PaddingBottom = UDim.new(0, props.padding / 2),
	})) else nil)
end
return {
	Label = Label,
}
