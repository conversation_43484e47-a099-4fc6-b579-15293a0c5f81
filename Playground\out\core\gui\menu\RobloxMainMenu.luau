-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _design = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design")
local COLORS = _design.COLORS
local BORDER_RADIUS = _design.BORDER_RADIUS
local TYPOGRAPHY = _design.TYPOGRAPHY
local RobloxPanel = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "panel", "RobloxPanel").RobloxPanel
local _RobloxGrid = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "RobloxGrid")
local RobloxGrid = _RobloxGrid.RobloxGrid
local RobloxGridItem = _RobloxGrid.RobloxGridItem
local PlayerInfoPanel = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "player", "PlayerInfoPanel").PlayerInfoPanel
local useZIndex = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "useZIndex").useZIndex
local function RobloxMainMenu(props)
	local selectedCategory, setSelectedCategory = React.useState("testing")
	local _binding = useZIndex("main-menu", true)
	local zIndex = _binding.zIndex
	-- Default categories matching your current debug system but with Roblox styling
	local defaultCategories = { {
		id = "testing",
		title = "🧪 World Testing Lab",
		icon = "🌍",
		color = COLORS.roblox.blue,
		items = { {
			id = "comprehensive-test",
			title = "🔬 Comprehensive Testing",
			description = "Run all Core framework tests in sequence with detailed console output for complete system diagnostics.",
			icon = "🔬",
			action = function()
				return print("Running comprehensive tests...")
			end,
		} },
	}, {
		id = "components",
		title = "🎨 GUI Component Browser",
		icon = "🎨",
		color = COLORS.roblox.purple,
		items = { {
			id = "component-showcase",
			title = "📱 Component Showcase",
			description = "Interactive showcase of all available GUI components including buttons, panels, sliders, inputs, and more. Perfect for UI development and testing.",
			icon = "📱",
			action = function()
				return print("Opening component showcase...")
			end,
		} },
	}, {
		id = "gravity",
		title = "🌌 Gravity Testing",
		icon = "🌌",
		color = COLORS.roblox.green,
		items = { {
			id = "low-gravity",
			title = "🌙 Low Gravity (0.3x)",
			description = "Test different gravity levels to see how they affect the entire world.",
			icon = "🌙",
			action = function()
				return print("Setting low gravity...")
			end,
		}, {
			id = "high-gravity",
			title = "🪐 High Gravity (2.5x)",
			description = "Experience heavy gravity effects.",
			icon = "🪐",
			action = function()
				return print("Setting high gravity...")
			end,
		}, {
			id = "zero-gravity",
			title = "🚀 Zero Gravity (0.05x)",
			description = "Float in space-like conditions.",
			icon = "🚀",
			action = function()
				return print("Setting zero gravity...")
			end,
		}, {
			id = "normal-gravity",
			title = "🌍 Normal Gravity (1.0x)",
			description = "Return to standard Earth-like gravity.",
			icon = "🌍",
			action = function()
				return print("Setting normal gravity...")
			end,
		} },
	}, {
		id = "weather",
		title = "🌦️ Weather Testing",
		icon = "🌦️",
		color = COLORS.roblox.orange,
		items = { {
			id = "rain",
			title = "🌧️ Rain Weather",
			description = "Test realistic rainfall with proper physics and sound effects.",
			icon = "🌧️",
			action = function()
				return print("Setting rain weather...")
			end,
		}, {
			id = "snow",
			title = "❄️ Snow Weather",
			description = "Experience winter conditions with snow particles.",
			icon = "❄️",
			action = function()
				return print("Setting snow weather...")
			end,
		}, {
			id = "clear",
			title = "☀️ Clear Weather",
			description = "Return to sunny, clear conditions.",
			icon = "☀️",
			action = function()
				return print("Setting clear weather...")
			end,
		} },
	} }
	local categories = props.categories or defaultCategories
	-- ▼ ReadonlyArray.find ▼
	local _callback = function(cat)
		return cat.id == selectedCategory
	end
	local _result
	for _i, _v in categories do
		if _callback(_v, _i - 1, categories) == true then
			_result = _v
			break
		end
	end
	-- ▲ ReadonlyArray.find ▲
	local _condition = _result
	if _condition == nil then
		_condition = categories[1]
	end
	local currentCategory = _condition
	if not props.isOpen then
		return React.createElement(React.Fragment)
	end
	local _exp = React.createElement(PlayerInfoPanel, {
		position = UDim2.new(0, 20, 0, 20),
		variant = "primary",
		zIndex = zIndex + 1,
	})
	local _exp_1 = React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.md),
	})
	local _exp_2 = React.createElement("uipadding", {
		PaddingTop = UDim.new(0, 16),
		PaddingBottom = UDim.new(0, 16),
		PaddingLeft = UDim.new(0, 12),
		PaddingRight = UDim.new(0, 12),
	})
	local _exp_3 = React.createElement("uilistlayout", {
		SortOrder = Enum.SortOrder.LayoutOrder,
		Padding = UDim.new(0, 8),
	})
	-- ▼ ReadonlyArray.map ▼
	local _newValue = table.create(#categories)
	local _callback_1 = function(category, index)
		return React.createElement(RobloxGridItem, {
			key = category.id,
			layoutOrder = index,
			variant = if selectedCategory == category.id then "accent" else "secondary",
			selected = selectedCategory == category.id,
			onClick = function()
				return setSelectedCategory(category.id)
			end,
		}, React.createElement("frame", {
			Size = UDim2.new(1, 0, 1, 0),
			BackgroundTransparency = 1,
			BorderSizePixel = 0,
		}, React.createElement("textlabel", {
			Size = UDim2.new(1, 0, 1, 0),
			BackgroundTransparency = 1,
			Text = category.title,
			TextColor3 = Color3.fromHex(COLORS.text.main),
			TextSize = TYPOGRAPHY.fontSize.sm,
			Font = TYPOGRAPHY.fontFamily.primary,
			TextXAlignment = Enum.TextXAlignment.Left,
			TextYAlignment = Enum.TextYAlignment.Center,
			TextTruncate = Enum.TextTruncate.AtEnd,
		})))
	end
	for _k, _v in categories do
		_newValue[_k] = _callback_1(_v, _k - 1, categories)
	end
	-- ▲ ReadonlyArray.map ▲
	local _exp_4 = React.createElement("frame", {
		Size = UDim2.new(0, 200, 1, 0),
		Position = UDim2.new(0, 0, 0, 0),
		BackgroundColor3 = Color3.fromHex(COLORS.bg.surface),
		BorderSizePixel = 0,
	}, _exp_1, _exp_2, React.createElement("scrollingframe", {
		Size = UDim2.new(1, 0, 1, 0),
		BackgroundTransparency = 1,
		BorderSizePixel = 0,
		ScrollBarThickness = 4,
		CanvasSize = UDim2.new(0, 0, 0, #categories * 60),
	}, _exp_3, _newValue))
	local _exp_5 = React.createElement("frame", {
		Size = UDim2.new(1, 0, 0, 60),
		Position = UDim2.new(0, 0, 0, 0),
		BackgroundTransparency = 1,
		BorderSizePixel = 0,
	}, React.createElement("textlabel", {
		Size = UDim2.new(1, 0, 1, 0),
		BackgroundTransparency = 1,
		Text = currentCategory.title,
		TextColor3 = Color3.fromHex(COLORS.text.main),
		TextSize = TYPOGRAPHY.fontSize["2xl"],
		Font = TYPOGRAPHY.fontFamily.secondary,
		TextXAlignment = Enum.TextXAlignment.Left,
		TextYAlignment = Enum.TextYAlignment.Center,
	}))
	local _exp_6 = currentCategory.items
	-- ▼ ReadonlyArray.map ▼
	local _newValue_1 = table.create(#_exp_6)
	local _callback_2 = function(item, index)
		return React.createElement(RobloxGridItem, {
			key = item.id,
			layoutOrder = index,
			variant = "primary",
			onClick = item.action,
			disabled = item.enabled == false,
		}, React.createElement("frame", {
			Size = UDim2.new(1, 0, 1, 0),
			BackgroundTransparency = 1,
			BorderSizePixel = 0,
		}, React.createElement("textlabel", {
			Size = UDim2.new(0, 32, 0, 32),
			Position = UDim2.new(0, 0, 0, 0),
			BackgroundTransparency = 1,
			Text = item.icon,
			TextSize = 24,
			Font = TYPOGRAPHY.fontFamily.primary,
			TextXAlignment = Enum.TextXAlignment.Center,
			TextYAlignment = Enum.TextYAlignment.Center,
		}), React.createElement("textlabel", {
			Size = UDim2.new(1, -40, 0, 20),
			Position = UDim2.new(0, 40, 0, 0),
			BackgroundTransparency = 1,
			Text = item.title,
			TextColor3 = Color3.fromHex(COLORS.text.main),
			TextSize = TYPOGRAPHY.fontSize.base,
			Font = TYPOGRAPHY.fontFamily.secondary,
			TextXAlignment = Enum.TextXAlignment.Left,
			TextYAlignment = Enum.TextYAlignment.Center,
			TextTruncate = Enum.TextTruncate.AtEnd,
		}), React.createElement("textlabel", {
			Size = UDim2.new(1, 0, 1, -24),
			Position = UDim2.new(0, 0, 0, 24),
			BackgroundTransparency = 1,
			Text = item.description,
			TextColor3 = Color3.fromHex(COLORS.text.secondary),
			TextSize = TYPOGRAPHY.fontSize.sm,
			Font = TYPOGRAPHY.fontFamily.primary,
			TextXAlignment = Enum.TextXAlignment.Left,
			TextYAlignment = Enum.TextYAlignment.Top,
			TextWrapped = true,
		})))
	end
	for _k, _v in _exp_6 do
		_newValue_1[_k] = _callback_2(_v, _k - 1, _exp_6)
	end
	-- ▲ ReadonlyArray.map ▲
	return React.createElement(React.Fragment, nil, _exp, React.createElement(RobloxPanel, {
		title = "🎮 Game Control Center",
		size = UDim2.new(0, 800, 0, 600),
		position = UDim2.new(0.5, 0, 0.5, 0),
		anchorPoint = Vector2.new(0.5, 0.5),
		variant = "primary",
		shadow = true,
		zIndex = zIndex,
		onClose = props.onClose,
	}, React.createElement("frame", {
		Size = UDim2.new(1, 0, 1, 0),
		BackgroundTransparency = 1,
		BorderSizePixel = 0,
	}, _exp_4, React.createElement("frame", {
		Size = UDim2.new(1, -220, 1, 0),
		Position = UDim2.new(0, 220, 0, 0),
		BackgroundTransparency = 1,
		BorderSizePixel = 0,
	}, _exp_5, React.createElement("frame", {
		Size = UDim2.new(1, 0, 1, -80),
		Position = UDim2.new(0, 0, 0, 80),
		BackgroundTransparency = 1,
		BorderSizePixel = 0,
	}, React.createElement(RobloxGrid, {
		columns = 2,
		cellSize = UDim2.new(0, 250, 0, 120),
		spacing = 16,
		variant = "transparent",
		responsive = true,
		responsiveColumns = {
			mobile = 1,
			tablet = 2,
			desktop = 2,
		},
	}, _newValue_1))))))
end
return {
	RobloxMainMenu = RobloxMainMenu,
}
