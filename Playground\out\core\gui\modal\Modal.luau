-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitFor<PERSON>hild("rbxts_include"):WaitFor<PERSON>hild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _design = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design")
local COLORS = _design.COLORS
local SIZES = _design.SIZES
local BORDER_RADIUS = _design.BORDER_RADIUS
local Overlay = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "overlay", "Overlay").Overlay
local ErrorBoundary = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "error-boundary").ErrorBoundary
local function Modal(props)
	local isAnimating, setIsAnimating = React.useState(false)
	local _condition = props.width
	if _condition == nil then
		_condition = SIZES.modal.medium.width
	end
	local width = _condition
	local _condition_1 = props.height
	if _condition_1 == nil then
		_condition_1 = SIZES.modal.medium.height
	end
	local height = _condition_1
	-- Handle open/close animations
	React.useEffect(function()
		if props.isOpen then
			setIsAnimating(true)
		end
	end, { props.isOpen })
	local handleClose = function()
		setIsAnimating(false)
		-- Delay actual close to allow animation
		task.delay(0.2, function()
			props.onClose()
		end)
	end
	if not props.isOpen and not isAnimating then
		return React.createElement(React.Fragment)
	end
	local modalScale = if isAnimating and props.isOpen then 1 else 0.9
	local modalTransparency = if isAnimating and props.isOpen then 0 else 0.3
	return React.createElement(Overlay, {
		onBackdropClick = handleClose,
		backgroundColor = Color3.fromRGB(0, 0, 0),
	}, React.createElement("textbutton", {
		Text = "",
		BackgroundColor3 = Color3.fromHex(COLORS.bg.base),
		Size = UDim2.new(0, width, 0, height),
		Position = UDim2.new(0.5, 0, 0.5, 0),
		AnchorPoint = Vector2.new(0.5, 0.5),
		ZIndex = 12,
		AutoButtonColor = false,
		BorderSizePixel = 0,
		BackgroundTransparency = modalTransparency,
		Event = {
			Activated = function()
				-- Stop propagation by doing nothing - this prevents backdrop click
			end,
		},
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.modal),
	}), React.createElement("uistroke", {
		Color = Color3.fromHex(COLORS.border.base),
		Thickness = 1,
		Transparency = 0.5,
	}), React.createElement("frame", {
		Size = UDim2.new(1, 8, 1, 8),
		Position = UDim2.new(0, 4, 0, 4),
		AnchorPoint = Vector2.new(0, 0),
		BackgroundColor3 = Color3.fromRGB(0, 0, 0),
		BackgroundTransparency = 0.9,
		BorderSizePixel = 0,
		ZIndex = -1,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.modal),
	})), React.createElement("frame", {
		Size = UDim2.new(1, 0, 0, 60),
		Position = UDim2.new(0, 0, 0, 0),
		BackgroundColor3 = Color3.fromHex(COLORS.bg.secondary),
		BorderSizePixel = 0,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.modal),
	}), React.createElement("frame", {
		Size = UDim2.new(1, 0, 0, BORDER_RADIUS.modal),
		Position = UDim2.new(0, 0, 1, -BORDER_RADIUS.modal),
		BackgroundColor3 = Color3.fromHex(COLORS.bg.secondary),
		BorderSizePixel = 0,
	}), React.createElement("uipadding", {
		PaddingLeft = UDim.new(0, SIZES.padding.lg),
		PaddingRight = UDim.new(0, SIZES.padding.lg),
		PaddingTop = UDim.new(0, SIZES.padding.md),
		PaddingBottom = UDim.new(0, SIZES.padding.md),
	}), React.createElement("textlabel", {
		Text = props.title,
		Size = UDim2.new(1, -50, 1, 0),
		Position = UDim2.new(0, 0, 0, 0),
		BackgroundTransparency = 1,
		TextColor3 = Color3.fromHex(COLORS.text.main),
		Font = Enum.Font.Gotham,
		TextSize = SIZES.fontSize.xl,
		TextXAlignment = Enum.TextXAlignment.Left,
		TextYAlignment = Enum.TextYAlignment.Center,
		TextWrapped = false,
		TextTruncate = Enum.TextTruncate.AtEnd,
	}), React.createElement("textbutton", {
		Text = "✕",
		Size = UDim2.new(0, 32, 0, 32),
		Position = UDim2.new(1, -40, 0.5, -16),
		BackgroundColor3 = Color3.fromHex(COLORS.bg.hover),
		BackgroundTransparency = 1,
		TextColor3 = Color3.fromHex(COLORS.text.secondary),
		Font = Enum.Font.Gotham,
		TextSize = SIZES.fontSize.lg,
		BorderSizePixel = 0,
		AutoButtonColor = false,
		Event = {
			Activated = handleClose,
			MouseEnter = function(rbx)
				rbx.BackgroundTransparency = 0
				rbx.TextColor3 = Color3.fromHex(COLORS.text.main)
			end,
			MouseLeave = function(rbx)
				rbx.BackgroundTransparency = 1
				rbx.TextColor3 = Color3.fromHex(COLORS.text.secondary)
			end,
		},
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.sm),
	}))), React.createElement("frame", {
		Size = UDim2.new(1, 0, 1, -60),
		Position = UDim2.new(0, 0, 0, 60),
		BackgroundTransparency = 1,
		BorderSizePixel = 0,
	}, React.createElement("uipadding", {
		PaddingLeft = UDim.new(0, SIZES.padding.lg),
		PaddingRight = UDim.new(0, SIZES.padding.lg),
		PaddingTop = UDim.new(0, SIZES.padding.md),
		PaddingBottom = UDim.new(0, SIZES.padding.lg),
	}), React.createElement(ErrorBoundary, {
		children = props.children,
	}))))
end
return {
	Modal = Modal,
}
