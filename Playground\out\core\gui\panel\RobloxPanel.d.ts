import * as React from "@rbxts/react";
interface RobloxPanelProps {
    children?: React.ReactNode;
    title?: string;
    size?: UDim2;
    position?: UDim2;
    anchorPoint?: Vector2;
    layoutOrder?: number;
    zIndex?: number;
    variant?: "primary" | "secondary" | "dark" | "light";
    shadow?: boolean;
    headerHeight?: number;
    padding?: number;
    responsive?: boolean;
    responsiveMargin?: boolean;
    onClose?: () => void;
}
export declare function RobloxPanel(props: RobloxPanelProps): React.ReactElement;
export {};
