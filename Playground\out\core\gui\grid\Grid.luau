-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local _design = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design")
local COLORS = _design.COLORS
local SIZES = _design.SIZES
local BORDER_RADIUS = _design.BORDER_RADIUS
local ResponsiveManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "ResponsiveManager").ResponsiveManager
local function Grid(props)
	local cellType = props.cellType or "card"
	local responsiveManager = ResponsiveManager:getInstance()
	local _condition = props.spacing
	if _condition == nil then
		_condition = SIZES.margin.sm
	end
	local spacing = _condition
	-- Calculate modern, responsive cell sizes
	local cellWidth
	local cellHeight
	if props.responsive then
		local screenSize = responsiveManager:getScreenSize()
		local deviceType = responsiveManager:getDeviceType()
		-- Enhanced base sizes for modern design
		local baseCellWidth = if cellType == "button" then 100 elseif cellType == "card" then 120 else SIZES.gridCell.width
		local baseCellHeight = if cellType == "button" then 60 elseif cellType == "card" then 80 else SIZES.gridCell.height
		-- Improved responsive scaling
		local scaleFactor = if deviceType == "mobile" then 0.85 elseif deviceType == "tablet" then 0.92 else 1.0
		cellWidth = baseCellWidth * scaleFactor
		cellHeight = baseCellHeight * scaleFactor
		-- Responsive max width handling
		local _value = props.maxWidth
		if _value ~= 0 and _value == _value and _value then
			local availableWidth = props.maxWidth - spacing * (props.cols - 1)
			local maxCellWidth = availableWidth / props.cols
			cellWidth = math.min(cellWidth, maxCellWidth)
		end
	else
		cellWidth = if cellType == "button" then 100 elseif cellType == "card" then 120 else SIZES.gridCell.width
		cellHeight = if cellType == "button" then 60 elseif cellType == "card" then 80 else SIZES.gridCell.height
	end
	local gridWidth = cellWidth * props.cols + spacing * (props.cols - 1)
	local gridHeight = cellHeight * props.rows + spacing * (props.rows - 1)
	-- Get modern styling for cell type
	local getCellStyling = function()
		repeat
			if cellType == "button" then
				return {
					backgroundColor = "transparent",
					backgroundTransparency = 1,
					cornerRadius = 0,
					borderColor = "transparent",
					borderThickness = 0,
					padding = 0,
				}
			end
			if cellType == "card" then
				return {
					backgroundColor = COLORS.bg.base,
					backgroundTransparency = 0,
					cornerRadius = BORDER_RADIUS.card,
					borderColor = COLORS.border.base,
					borderThickness = 1,
					padding = SIZES.padding.sm,
				}
			end
			return {
				backgroundColor = COLORS.bg.secondary,
				backgroundTransparency = 0,
				cornerRadius = BORDER_RADIUS.sm,
				borderColor = COLORS.border.l2,
				borderThickness = 1,
				padding = SIZES.padding.xs,
			}
		until true
	end
	local cellStyling = getCellStyling()
	local _exp = React.createElement("uigridlayout", {
		CellPadding = UDim2.new(0, spacing, 0, spacing),
		CellSize = UDim2.new(0, cellWidth, 0, cellHeight),
		SortOrder = Enum.SortOrder.LayoutOrder,
		HorizontalAlignment = Enum.HorizontalAlignment.Center,
		VerticalAlignment = Enum.VerticalAlignment.Center,
	})
	local _result = props.children
	if _result ~= nil then
		-- ▼ ReadonlyArray.map ▼
		local _newValue = table.create(#_result)
		local _callback = function(child, index)
			return React.createElement("frame", {
				key = `grid-cell-{index}`,
				Size = UDim2.new(1, 0, 1, 0),
				BackgroundColor3 = Color3.fromHex(cellStyling.backgroundColor),
				BackgroundTransparency = cellStyling.backgroundTransparency,
				BorderSizePixel = 0,
				LayoutOrder = index,
			}, cellStyling.cornerRadius > 0 and (React.createElement("uicorner", {
				CornerRadius = UDim.new(0, cellStyling.cornerRadius),
			})), cellStyling.borderThickness > 0 and (React.createElement("uistroke", {
				Color = Color3.fromHex(cellStyling.borderColor),
				Thickness = cellStyling.borderThickness,
				Transparency = 0.3,
			})), props.elevation and cellType == "card" and (React.createElement("frame", {
				Size = UDim2.new(1, 2, 1, 2),
				Position = UDim2.new(0, 1, 0, 1),
				BackgroundColor3 = Color3.fromRGB(0, 0, 0),
				BackgroundTransparency = 0.95,
				BorderSizePixel = 0,
				ZIndex = -1,
			}, React.createElement("uicorner", {
				CornerRadius = UDim.new(0, cellStyling.cornerRadius),
			}))), cellStyling.padding > 0 and (React.createElement("uipadding", {
				PaddingLeft = UDim.new(0, cellStyling.padding),
				PaddingRight = UDim.new(0, cellStyling.padding),
				PaddingTop = UDim.new(0, cellStyling.padding),
				PaddingBottom = UDim.new(0, cellStyling.padding),
			})), child)
		end
		for _k, _v in _result do
			_newValue[_k] = _callback(_v, _k - 1, _result)
		end
		-- ▲ ReadonlyArray.map ▲
		_result = _newValue
	end
	return React.createElement("frame", {
		Size = UDim2.new(0, gridWidth, 0, gridHeight),
		Position = UDim2.new(0, 0, 0, 0),
		BackgroundTransparency = 1,
		BorderSizePixel = 0,
		LayoutOrder = props.layoutOrder,
	}, _exp, _result)
end
return {
	Grid = Grid,
}
