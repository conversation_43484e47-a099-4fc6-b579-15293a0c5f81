export const COLORS = {
	// Modern Roblox-style bright, kid-friendly color palette
	text: {
		main: "#000000", // Black text on white backgrounds for better readability
		secondary: "#525252", // Medium grey for secondary text
		muted: "#737373", // Light grey for muted text
		inverse: "#FFFFFF", // White text for dark backgrounds
		accent: "#00A2FF", // Bright blue for accent text
	},
	bg: {
		base: "#FFFFFF", // Clean white background (modern Roblox standard)
		secondary: "#F8FAFC", // Very light grey for cards/panels
		surface: "#FFFFFF", // White surface color
		"surface-hover": "#F1F5F9", // Light hover state
		"surface-pressed": "#E2E8F0", // Pressed state
		avatar: "#E5E7EB", // Light grey for avatar backgrounds
		badge: "#00A2FF", // Bright blue for badges
		hover: "#F1F5F9", // Consistent hover background
		modal: "#000000", // Dark overlay (transparency handled separately)
		card: "#FFFFFF", // White card backgrounds
		accent: "#F0F9FF", // Light blue accent background
	},
	label: {
		text: "#000000", // Black text for labels
		focus: "#00A2FF", // Bright blue for focused labels
		bg: "#FFFFFF", // White label backgrounds
		border: "#D1D5DB", // Light grey borders
		muted: "#6B7280", // Muted grey text
		hover: "#F9FAFB", // Light hover background
	},
	span: {
		default: "#000000", // Default black text
		muted: "#6B7280", // Muted grey text
		highlight: "#00A2FF", // Bright blue highlights
		subtle: "#9CA3AF", // Subtle grey text
		hover: "#374151", // Darker on hover
	},
	border: {
		base: "#E5E7EB", // Light grey default borders
		l1: "#F3F4F6", // Very light borders
		l2: "#D1D5DB", // Medium light borders
		l3: "#9CA3AF", // Medium borders
		strong: "#6B7280", // Strong borders
		focus: "#00A2FF", // Bright blue focus borders
		danger: "#EF4444", // Red danger borders
		success: "#10B981", // Green success borders
	},
	// Primary brand colors - modern Roblox style
	primary: "#00A2FF", // Bright Roblox blue
	"primary-dark": "#0284C7", // Darker blue
	"primary-light": "#38BDF8", // Lighter blue
	"primary-hover": "#0EA5E9", // Blue hover state
	
	// Status colors - vibrant and kid-friendly
	success: "#10B981", // Bright green
	"success-dark": "#059669", // Darker green
	"success-light": "#34D399", // Lighter green
	"success-hover": "#059669", // Green hover
	
	warning: "#F59E0B", // Bright orange/yellow
	"warning-dark": "#D97706", // Darker orange
	"warning-light": "#FCD34D", // Lighter yellow
	"warning-hover": "#D97706", // Orange hover
	
	error: "#EF4444", // Bright red
	"error-dark": "#DC2626", // Darker red
	"error-light": "#F87171", // Lighter red
	"error-hover": "#DC2626", // Red hover
	
	info: "#3B82F6", // Blue info
	"info-dark": "#2563EB", // Darker blue
	"info-light": "#60A5FA", // Lighter blue
	"info-hover": "#2563EB", // Blue hover
	
	// UI element colors
	"progress-bg": "#E5E7EB", // Light grey progress background
	"progress-fill": "#00A2FF", // Blue progress fill
	"account-active": "#F0F9FF", // Light blue active state
	"account-active-hover": "#E0F2FE", // Lighter blue hover
	
	badge: {
		bg: "#00A2FF", // Blue badge background
		text: "#FFFFFF", // White badge text
		border: "#0284C7", // Darker blue border
		success: "#10B981", // Green success badge
		warning: "#F59E0B", // Orange warning badge
		error: "#EF4444", // Red error badge
	},
	
	ring: {
		"focus-accent": "#00A2FF", // Blue focus ring
	},
	
	shadow: {
		sm: "#000000", // Very light shadows (transparency handled separately)
		md: "#000000", // Light shadows (transparency handled separately)
		lg: "#000000", // Medium shadows (transparency handled separately)
		xl: "#000000", // Stronger shadows (transparency handled separately)
	},
	
	// Vibrant color variants for different UI elements
	red: "#EF4444",
	"red-hover": "#DC2626",
	"red-light": "#F87171",
	
	blue: "#00A2FF",
	"blue-hover": "#0EA5E9",
	"blue-light": "#38BDF8",
	
	green: "#10B981",
	"green-hover": "#059669",
	"green-light": "#34D399",
	
	yellow: "#F59E0B",
	"yellow-hover": "#D97706",
	"yellow-light": "#FCD34D",
	
	purple: "#8B5CF6",
	"purple-hover": "#7C3AED",
	"purple-light": "#A78BFA",
	
	pink: "#EC4899",
	"pink-hover": "#DB2777",
	"pink-light": "#F472B6",
	
	gray: "#6B7280",
	"gray-hover": "#4B5563",
	"gray-light": "#9CA3AF",
	
	// Special Roblox-themed colors
	"roblox-red": "#FF6B6B", // Playful red
	"roblox-blue": "#4ECDC4", // Playful teal
	"roblox-green": "#95E1D3", // Playful mint
	"roblox-orange": "#FFB347", // Playful orange
	"roblox-purple": "#B19CD9", // Playful purple
	
	// Toast and notification colors
	"toast-success": "#10B981",
	"toast-warning": "#F59E0B", 
	"toast-error": "#EF4444",
	"toast-info": "#3B82F6",
	
	// Status overlay colors
	"status-overlay": "#1E40AF",
	"alert-overlay": "#EF4444", // Red alert overlay (transparency handled separately)
};
