-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local Players = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services").Players
local _design = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design")
local COLORS = _design.COLORS
local SIZES = _design.SIZES
local BORDER_RADIUS = _design.BORDER_RADIUS
local TYPOGRAPHY = _design.TYPOGRAPHY
local ResponsiveManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "ResponsiveManager").ResponsiveManager
local function PlayerInfoPanel(props)
	local player = props.player or Players.LocalPlayer
	local playerStats, setPlayerStats = React.useState({
		level = 1,
		experience = 0,
		robux = 0,
		playtime = "0h 0m",
	})
	local responsiveManager = React.useMemo(function()
		local _exitType, _returns = TS.try(function()
			return TS.TRY_RETURN, { ResponsiveManager:getInstance() }
		end, function(error)
			warn(`❌ Failed to get ResponsiveManager: {error}`)
			return TS.TRY_RETURN, { nil }
		end)
		if _exitType then
			return unpack(_returns)
		end
	end, {})
	-- Mock player stats (in real implementation, fetch from data service)
	React.useEffect(function()
		-- Simulate loading player stats
		setPlayerStats({
			level = 25,
			experience = 1250,
			robux = 150,
			playtime = "2h 30m",
		})
	end, { player })
	-- Calculate responsive values
	local variant = props.variant or "primary"
	local _condition = props.showAvatar
	if _condition == nil then
		_condition = true
	end
	local showAvatar = _condition
	local _condition_1 = props.showLevel
	if _condition_1 == nil then
		_condition_1 = true
	end
	local showLevel = _condition_1
	local _condition_2 = props.showStats
	if _condition_2 == nil then
		_condition_2 = (variant == "detailed")
	end
	local showStats = _condition_2
	local _condition_3 = props.showRobux
	if _condition_3 == nil then
		_condition_3 = (variant == "detailed")
	end
	local showRobux = _condition_3
	-- Get variant-based sizing
	local getVariantSize = function()
		repeat
			local _fallthrough = false
			if variant == "compact" then
				return UDim2.new(0, 200, 0, 60)
			end
			if variant == "detailed" then
				return UDim2.new(0, 280, 0, 120)
			end
			if variant == "primary" then
			end
			return UDim2.new(0, 240, 0, 80)
		until true
	end
	local size = props.size or getVariantSize()
	local avatarSize = if variant == "compact" then SIZES.avatar.small else SIZES.avatar.medium
	local _attributes = {
		BackgroundColor3 = Color3.fromHex(COLORS.roblox.panel),
		BackgroundTransparency = COLORS.transparency.panel,
		Size = size,
		Position = props.position or UDim2.new(0, 20, 0, 20),
		AnchorPoint = props.anchorPoint or Vector2.new(0, 0),
	}
	local _condition_4 = props.zIndex
	if _condition_4 == nil then
		_condition_4 = 10
	end
	_attributes.ZIndex = _condition_4
	_attributes.BorderSizePixel = 0
	_attributes.ClipsDescendants = true
	local _exp = React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.panel),
	})
	local _exp_1 = React.createElement("uistroke", {
		Color = Color3.fromHex(COLORS.roblox["panel-border"]),
		Thickness = 1,
		Transparency = COLORS.transparency["panel-border"],
	})
	local _exp_2 = React.createElement("frame", {
		Size = UDim2.new(1, 4, 1, 4),
		Position = UDim2.new(0, 2, 0, 2),
		AnchorPoint = Vector2.new(0, 0),
		BackgroundColor3 = Color3.fromHex(COLORS.roblox["panel-shadow"]),
		BackgroundTransparency = COLORS.transparency["panel-shadow"],
		BorderSizePixel = 0,
		ZIndex = -1,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.panel),
	}))
	local _exp_3 = React.createElement("uipadding", {
		PaddingTop = UDim.new(0, 12),
		PaddingBottom = UDim.new(0, 12),
		PaddingLeft = UDim.new(0, 12),
		PaddingRight = UDim.new(0, 12),
	})
	local _exp_4 = showAvatar and (React.createElement("frame", {
		Size = UDim2.new(0, avatarSize, 0, avatarSize),
		Position = UDim2.new(0, 0, 0, 0),
		BackgroundColor3 = Color3.fromHex(COLORS.bg.avatar),
		BorderSizePixel = 0,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.avatar),
	}), React.createElement("textlabel", {
		Size = UDim2.new(1, 0, 1, 0),
		BackgroundTransparency = 1,
		Text = "👤",
		TextColor3 = Color3.fromHex(COLORS.text.main),
		TextSize = avatarSize * 0.6,
		Font = TYPOGRAPHY.fontFamily.primary,
		TextXAlignment = Enum.TextXAlignment.Center,
		TextYAlignment = Enum.TextYAlignment.Center,
	})))
	local _attributes_1 = {
		Size = UDim2.new(1, 0, 0, 20),
		Position = UDim2.new(0, 0, 0, 0),
		BackgroundTransparency = 1,
	}
	local _result = player
	if _result ~= nil then
		_result = _result.Name
	end
	local _condition_5 = _result
	if _condition_5 == nil then
		_condition_5 = "Player"
	end
	_attributes_1.Text = _condition_5
	_attributes_1.TextColor3 = Color3.fromHex(COLORS.text.main)
	_attributes_1.TextSize = TYPOGRAPHY.fontSize.lg
	_attributes_1.Font = TYPOGRAPHY.fontFamily.secondary
	_attributes_1.TextXAlignment = Enum.TextXAlignment.Left
	_attributes_1.TextYAlignment = Enum.TextYAlignment.Center
	_attributes_1.TextTruncate = Enum.TextTruncate.AtEnd
	return React.createElement("frame", _attributes, _exp, _exp_1, _exp_2, _exp_3, React.createElement("frame", {
		Size = UDim2.new(1, 0, 1, 0),
		BackgroundTransparency = 1,
		BorderSizePixel = 0,
	}, _exp_4, React.createElement("frame", {
		Size = UDim2.new(1, if showAvatar then -(avatarSize + 8) else 0, 1, 0),
		Position = UDim2.new(0, if showAvatar then avatarSize + 8 else 0, 0, 0),
		BackgroundTransparency = 1,
		BorderSizePixel = 0,
	}, React.createElement("textlabel", _attributes_1), showLevel and (React.createElement("frame", {
		Size = UDim2.new(0, 60, 0, 16),
		Position = UDim2.new(0, 0, 0, 24),
		BackgroundColor3 = Color3.fromHex(COLORS.roblox.blue),
		BorderSizePixel = 0,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, 8),
	}), React.createElement("textlabel", {
		Size = UDim2.new(1, 0, 1, 0),
		BackgroundTransparency = 1,
		Text = `LV {playerStats.level}`,
		TextColor3 = Color3.fromHex(COLORS.text.main),
		TextSize = TYPOGRAPHY.fontSize.sm,
		Font = TYPOGRAPHY.fontFamily.primary,
		TextXAlignment = Enum.TextXAlignment.Center,
		TextYAlignment = Enum.TextYAlignment.Center,
	}))), showStats and variant == "detailed" and (React.createElement("frame", {
		Size = UDim2.new(1, 0, 0, 40),
		Position = UDim2.new(0, 0, 1, -40),
		BackgroundTransparency = 1,
		BorderSizePixel = 0,
	}, React.createElement("textlabel", {
		Size = UDim2.new(0.5, -4, 0, 18),
		Position = UDim2.new(0, 0, 0, 0),
		BackgroundTransparency = 1,
		Text = `XP: {playerStats.experience}`,
		TextColor3 = Color3.fromHex(COLORS.text.secondary),
		TextSize = TYPOGRAPHY.fontSize.sm,
		Font = TYPOGRAPHY.fontFamily.primary,
		TextXAlignment = Enum.TextXAlignment.Left,
		TextYAlignment = Enum.TextYAlignment.Center,
	}), React.createElement("textlabel", {
		Size = UDim2.new(0.5, -4, 0, 18),
		Position = UDim2.new(0.5, 4, 0, 0),
		BackgroundTransparency = 1,
		Text = `Time: {playerStats.playtime}`,
		TextColor3 = Color3.fromHex(COLORS.text.secondary),
		TextSize = TYPOGRAPHY.fontSize.sm,
		Font = TYPOGRAPHY.fontFamily.primary,
		TextXAlignment = Enum.TextXAlignment.Left,
		TextYAlignment = Enum.TextYAlignment.Center,
	}), showRobux and (React.createElement("frame", {
		Size = UDim2.new(0, 80, 0, 18),
		Position = UDim2.new(0, 0, 0, 22),
		BackgroundColor3 = Color3.fromHex(COLORS.roblox.green),
		BorderSizePixel = 0,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, 4),
	}), React.createElement("textlabel", {
		Size = UDim2.new(1, 0, 1, 0),
		BackgroundTransparency = 1,
		Text = `R$ {playerStats.robux}`,
		TextColor3 = Color3.fromHex(COLORS.text.main),
		TextSize = TYPOGRAPHY.fontSize.sm,
		Font = TYPOGRAPHY.fontFamily.primary,
		TextXAlignment = Enum.TextXAlignment.Center,
		TextYAlignment = Enum.TextYAlignment.Center,
	}))))))))
end
return {
	PlayerInfoPanel = PlayerInfoPanel,
}
