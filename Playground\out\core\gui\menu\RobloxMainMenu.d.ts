import * as React from "@rbxts/react";
interface MenuCategory {
    id: string;
    title: string;
    icon: string;
    color: string;
    items: MenuItem[];
}
interface MenuItem {
    id: string;
    title: string;
    description: string;
    icon: string;
    action: () => void;
    enabled?: boolean;
}
interface RobloxMainMenuProps {
    isOpen: boolean;
    onClose: () => void;
    categories?: MenuCategory[];
}
export declare function RobloxMainMenu(props: RobloxMainMenuProps): React.ReactElement;
export {};
