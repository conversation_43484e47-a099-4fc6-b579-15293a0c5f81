import * as React from "@rbxts/react";
import { createRoot } from "@rbxts/react-roblox";
import { Players, ReplicatedStorage } from "@rbxts/services";
import { ElementalBattleUI } from "./gui/ElementalBattleUI";
import { MovementExample } from "./movement/MovementExample";
import {
	initializeDebugSystem,
	initializeClientCore,
	SplashScreen,
	useSplashScreen,
	ToastManager,
	ToastService,
	ErrorBoundary,
	COLORS,
	SIZES,
	TYPOGRAPHY,
	BORDER_RADIUS,
} from "../core";
import { QuickUtils } from "../core/helper/GameUtils";

const version = "v1.3.5";
const player = Players.LocalPlayer;
const playerGui = player.WaitForChild("PlayerGui") as PlayerGui;

// Create a ScreenGui with properties to make it visible
const screenGui = new Instance("ScreenGui", playerGui);
screenGui.ResetOnSpawn = false;
screenGui.IgnoreGuiInset = true;
screenGui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling;
screenGui.DisplayOrder = 100; // Higher than DebugGUI to ensure interactive panels appear on top
screenGui.Name = "MainReactGUI";

print(`🎮 [${tick()}] Main React ScreenGui created with DisplayOrder: ${screenGui.DisplayOrder}`);

// Main App Component with Splash Screen - Focus on Elemental Battle Game
function MainApp() {
	const { state, startLoading, hide, setupDefaultTasks, manager } = useSplashScreen();
	const [hasStarted, setHasStarted] = React.useState(false);
	const [gameUIOpen, setGameUIOpen] = React.useState(false);

	// Setup and start loading when component mounts
	React.useEffect(() => {
		if (!hasStarted) {
			print("🚀 MainApp: Starting initialization process...");

			// Setup default core framework loading tasks
			setupDefaultTasks();

			// Add custom game loading tasks with better error handling
			manager.addLoadingTask({
				name: "Waiting for Server...",
				weight: 1,
				task: async () => {
					try {
						print("🔍 Waiting for server to be ready...");
						// Wait for server initialization indicator
						const serverIndicator = ReplicatedStorage.WaitForChild(
							"CoreServerInitialized",
							30,
						) as BoolValue;
						if (serverIndicator && serverIndicator.Value) {
							print("✅ Server is ready!");
						} else {
							warn("⚠️ Server not ready, but continuing...");
						}
					} catch (error) {
						warn(`⚠️ Failed to detect server readiness: ${error}`);
						// Continue anyway
					}
				},
			});

			manager.addLoadingTask({
				name: "Loading Game Systems...",
				weight: 2,
				task: async () => {
					try {
						// Initialize debug system for development
						print("🔧 Initializing debug system...");
						initializeDebugSystem();

						// Initialize client core
						print("🏗️ Initializing client core...");
						const clientCoreResult = await initializeClientCore();
						if (clientCoreResult.isError()) {
							const errorMessage = `Failed to initialize client core: ${clientCoreResult.getError().message}`;
							warn(errorMessage);
							throw errorMessage;
						}

						print("✅ Game systems loaded successfully");
					} catch (error) {
						warn(`⚠️ Game systems loading failed: ${error}`);
						// Don't throw - allow the app to continue with limited functionality
					}
				},
			});

			manager.addLoadingTask({
				name: "Initializing Movement System...",
				weight: 1,
				task: () => {
					try {
						// Initialize movement example for testing
						print("🏃 Initializing movement system...");
						new MovementExample();
						print("✅ Movement system initialized");
					} catch (error) {
						warn(`⚠️ Movement system initialization failed: ${error}`);
						// Continue without movement - not critical for UI
					}
				},
			});

			manager.addLoadingTask({
				name: "Finalizing Elemental Battle Setup...",
				weight: 1,
				task: async () => {
					try {
						print(`⚔️ Elemental Battle Arena loaded! [${version}]`);
						print(`🎮 Press Enter or click the floating button to open the game menu`);

						// Validate critical systems are working
						const players = game.GetService("Players");
						const localPlayer = players.LocalPlayer;
						if (!localPlayer) {
							warn("⚠️ LocalPlayer not available - some features may not work");
						}

						// Show success toast when loading is complete (with delay to ensure toast system is ready)
						task.delay(0.5, () => {
							try {
								ToastService.showSuccess(
									"⚔️ Elemental Battle Arena",
									`Ready for epic battles! v${version}`,
								);
							} catch (toastError) {
								warn(`⚠️ Failed to show welcome toast: ${toastError}`);
							}
						});

						print("🎯 Elemental Battle client setup finalized successfully");
					} catch (error) {
						warn(`⚠️ Client setup finalization failed: ${error}`);
						// Continue anyway - basic functionality should still work
					}
				},
			});

			// Start the loading process
			print("🎬 Starting loading process...");
			startLoading().catch((loadingError) => {
				warn(`❌ Loading process failed: ${loadingError}`);
				// Force hide splash screen if loading completely fails
				task.delay(2, () => {
					hide();
					ToastService.showError(
						"Loading Error",
						"Some systems failed to load, but the application is still functional.",
					);
				});
			});

			setHasStarted(true);
		}
	}, [hasStarted, setupDefaultTasks, manager, startLoading]);

	const handleLoadingComplete = React.useCallback(() => {
		// Called when loading is complete and splash screen should hide
		hide();
		print("🎉 Splash screen loading completed!");
		// Auto-open the game UI after loading
		task.delay(1, () => {
			setGameUIOpen(true);
		});
	}, [hide]);

	// Handle keyboard input for opening game UI
	React.useEffect(() => {
		const UserInputService = game.GetService("UserInputService");

		const connection = UserInputService.InputBegan.Connect((input, gameProcessed) => {
			if (gameProcessed) return;

			if (input.UserInputType === Enum.UserInputType.Keyboard) {
				if (input.KeyCode === Enum.KeyCode.Return) {
					// Enter key
					setGameUIOpen(true);
				}
			}
		});

		return () => connection.Disconnect();
	}, []);

	return (
		<ErrorBoundary
			onError={(err, errorId) => {
				warn(`[MainApp] Critical error in main application: ${err} (ID: ${errorId})`);
				// Don't crash the entire app - show a toast notification instead
				try {
					ToastService.showError("Application Error", `A component error occurred. Error ID: ${errorId}`);
				} catch (toastError) {
					warn(`[MainApp] Failed to show error toast: ${toastError}`);
				}
			}}
		>
			<>
				<ErrorBoundary
					onError={(err, errorId) => {
						warn(`[SplashScreen] Error in splash screen: ${err} (ID: ${errorId})`);
					}}
				>
					<SplashScreen
						isVisible={state.isVisible}
						loadingProgress={state.loadingProgress}
						loadingText={state.loadingText}
						onLoadingComplete={handleLoadingComplete}
					/>
				</ErrorBoundary>

				{/* Toast notification system - always visible with its own error boundary */}
				<ErrorBoundary
					onError={(err, errorId) => {
						warn(`[ToastManager] Error in toast system: ${err} (ID: ${errorId})`);
					}}
				>
					<ToastManager />
				</ErrorBoundary>

				{/* Main game UI - only show when splash is hidden */}
				{!state.isVisible && (
					<>
						{/* Floating Game Button */}
						<ErrorBoundary
							onError={(err, errorId) => {
								warn(`[FloatingButton] Error in floating button: ${err} (ID: ${errorId})`);
							}}
						>
							<frame
								Size={new UDim2(0, 90, 0, 90)} // Slightly larger for better touch target
								Position={new UDim2(0, 20, 1, -110)}
								AnchorPoint={new Vector2(0, 1)}
								BackgroundColor3={QuickUtils.safeColor(COLORS.primary)} // Use new primary color
								BorderSizePixel={0}
							>
								<uicorner CornerRadius={new UDim(0, 45)} /> {/* Perfectly circular */}
								<uistroke Color={QuickUtils.safeColor(COLORS.warning)} Thickness={3} /> {/* Bright orange accent */}
								
								{/* Enhanced drop shadow */}
								<frame
									Size={new UDim2(1, 6, 1, 6)}
									Position={new UDim2(0, 3, 0, 3)}
									AnchorPoint={new Vector2(0, 0)}
									BackgroundColor3={Color3.fromRGB(0, 0, 0)}
									BackgroundTransparency={0.8}
									BorderSizePixel={0}
									ZIndex={-1}
								>
									<uicorner CornerRadius={new UDim(0, 48)} />
								</frame>
								
								<textbutton
									Size={new UDim2(1, 0, 1, 0)}
									Position={new UDim2(0, 0, 0, 0)}
									BackgroundTransparency={1}
									Text="⚔️"
									TextSize={36} // Larger icon
									TextColor3={QuickUtils.safeColor(COLORS.text.inverse)}
									Font={Enum.Font.FredokaOne}
									Event={{
										MouseButton1Click: () => setGameUIOpen(true),
										MouseEnter: (rbx) => {
											// Hover effect - cast to frame to access BackgroundColor3
											const frame = rbx.Parent as Frame;
											if (frame) frame.BackgroundColor3 = QuickUtils.safeColor(COLORS["primary-hover"]);
										},
										MouseLeave: (rbx) => {
											// Reset color
											const frame = rbx.Parent as Frame;
											if (frame) frame.BackgroundColor3 = QuickUtils.safeColor(COLORS.primary);
										},
									}}
								/>
								
								{/* Floating label with modern styling */}
								<textlabel
									Size={new UDim2(0, 220, 0, 24)}
									Position={new UDim2(1, 15, 0.5, -12)}
									AnchorPoint={new Vector2(0, 0.5)}
									BackgroundColor3={QuickUtils.safeColor(COLORS.bg.base)}
									BackgroundTransparency={0.1}
									BorderSizePixel={0}
									Text="⚔️ Elemental Battle Arena"
									TextSize={SIZES.fontSize.md}
									TextColor3={QuickUtils.safeColor(COLORS.text.main)}
									Font={Enum.Font.Gotham}
									// FontWeight handled by font selection
									TextXAlignment={Enum.TextXAlignment.Left}
									TextStrokeTransparency={1} // Remove stroke for clean look
								>
									<uicorner CornerRadius={new UDim(0, BORDER_RADIUS.sm)} />
									<uistroke Color={QuickUtils.safeColor(COLORS.border.base)} Thickness={1} Transparency={0.5} />
									<uipadding
										PaddingLeft={new UDim(0, SIZES.padding.sm)}
										PaddingRight={new UDim(0, SIZES.padding.sm)}
									/>
								</textlabel>
							</frame>
						</ErrorBoundary>

						{/* Elemental Battle Game UI */}
						<ErrorBoundary
							onError={(err, errorId) => {
								warn(`[ElementalBattleUI] Error in elemental battle UI: ${err} (ID: ${errorId})`);
								try {
									ToastService.showError("Game UI Error", "The game interface encountered an error.");
								} catch (toastError) {
									warn(`[ElementalBattleUI] Failed to show error toast: ${toastError}`);
								}
							}}
						>
							<ElementalBattleUI isOpen={gameUIOpen} onClose={() => setGameUIOpen(false)} />
						</ErrorBoundary>
					</>
				)}
			</>
		</ErrorBoundary>
	);
}

const root = createRoot(screenGui);

// Render the main app with splash screen
root.render(<MainApp />);

// All initialization is now handled by the splash screen system
