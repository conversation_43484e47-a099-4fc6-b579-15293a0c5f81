-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local COLORS = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design").COLORS
local QuickUtils = TS.import(script, game:GetService("ReplicatedStorage"), "core", "helper", "GameUtils").QuickUtils
local function ProgressBar(_param)
	local progress = _param.progress
	local barColor = _param.barColor
	if barColor == nil then
		barColor = QuickUtils.safeColor(COLORS.primary)
	end
	local backgroundColor = _param.backgroundColor
	if backgroundColor == nil then
		backgroundColor = QuickUtils.safeColor(COLORS.bg.secondary)
	end
	local size = _param.size
	if size == nil then
		size = UDim2.new(1, 0, 0, 20)
	end
	local cornerRadius = _param.cornerRadius
	if cornerRadius == nil then
		cornerRadius = UDim.new(0, 4)
	end
	local borderSize = _param.borderSize
	if borderSize == nil then
		borderSize = 0
	end
	local borderColor = _param.borderColor
	if borderColor == nil then
		borderColor = QuickUtils.safeColor(COLORS.border.base)
	end
	local layoutOrder = _param.layoutOrder
	local position = _param.position
	local anchorPoint = _param.anchorPoint
	-- Clamp progress between 0 and 1
	local clampedProgress = math.max(0, math.min(1, progress))
	return React.createElement("frame", {
		Size = size,
		Position = position,
		AnchorPoint = anchorPoint,
		BackgroundColor3 = backgroundColor,
		BorderSizePixel = borderSize,
		BorderColor3 = borderColor,
		LayoutOrder = layoutOrder,
	}, React.createElement("uicorner", {
		CornerRadius = cornerRadius,
	}), React.createElement("frame", {
		Size = UDim2.new(clampedProgress, 0, 1, 0),
		Position = UDim2.new(0, 0, 0, 0),
		BackgroundColor3 = barColor,
		BorderSizePixel = 0,
	}, React.createElement("uicorner", {
		CornerRadius = cornerRadius,
	})))
end
return {
	ProgressBar = ProgressBar,
}
