export const TYPOGRAPHY = {
	// Primary fonts - more engaging and kid-friendly
	font: {
		primary: Enum.Font.Gotham, // Clean, modern primary font
		secondary: Enum.Font.GothamBold, // Bold variant
		heading: Enum.Font.FredokaOne, // Fun, playful font for headings
		body: Enum.Font.Gotham, // Readable body font
		ui: Enum.Font.Gotham, // UI elements font
		mono: Enum.Font.RobotoMono, // Monospace for code/numbers
	},
	
	weight: {
		regular: Enum.FontWeight.Regular,
		medium: Enum.FontWeight.Medium,
		semibold: Enum.FontWeight.SemiBold,
		bold: Enum.FontWeight.Bold,
		extrabold: Enum.FontWeight.ExtraBold,
	},
	
	// Font families with fallbacks
	fontFamily: {
		primary: Enum.Font.Gotham, // Primary UI font
		display: Enum.Font.FredokaOne, // For headings and titles
		playful: Enum.Font.Cartoon, // For fun, kid-focused elements
		readable: Enum.Font.SourceSans, // For large text blocks
		technical: Enum.Font.RobotoMono, // For technical/number displays
	},
	
	// Text sizing scale
	size: {
		xs: 10,
		sm: 12,
		base: 14, // Default size
		lg: 16,
		xl: 18,
		"2xl": 20,
		"3xl": 24,
		"4xl": 28,
		"5xl": 32,
	},
	
	// Line heights for better readability
	lineHeight: {
		tight: 1.1,
		normal: 1.4,
		relaxed: 1.6,
		loose: 1.8,
	},
	
	// Letter spacing for different text types
	letterSpacing: {
		tight: -0.5,
		normal: 0,
		wide: 0.5,
		wider: 1,
	},
};
