-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local COLORS = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design").COLORS
local ResponsiveManager = TS.import(script, game:GetService("ReplicatedStorage"), "core", "gui", "layout", "ResponsiveManager").ResponsiveManager
local QuickUtils = TS.import(script, game:GetService("ReplicatedStorage"), "core", "helper", "GameUtils").QuickUtils
local function VerticalFrame(props)
	local _condition = props.backgroundColor
	if _condition == nil then
		_condition = COLORS.bg.base
	end
	local backgroundColor = _condition
	local _condition_1 = props.backgroundTransparency
	if _condition_1 == nil then
		_condition_1 = 1
	end
	local backgroundTransparency = _condition_1
	-- Responsive manager for dynamic calculations
	local responsiveManager = ResponsiveManager:getInstance()
	-- Calculate responsive padding and spacing
	local _condition_2 = props.padding
	if _condition_2 == nil then
		_condition_2 = 10
	end
	local basePadding = _condition_2
	local _condition_3 = props.spacing
	if _condition_3 == nil then
		_condition_3 = 6
	end
	local baseSpacing = _condition_3
	local padding = if props.responsiveMargin then responsiveManager:getResponsiveMargin(basePadding) else basePadding
	local spacing = if props.responsiveMargin then responsiveManager:getResponsiveMargin(baseSpacing) else baseSpacing
	-- Smart sizing: if fitContent is true or no size specified, use AutomaticSize
	local _condition_4 = props.fitContent
	if _condition_4 == nil then
		_condition_4 = props.size == nil
	end
	local fitContent = _condition_4
	local size = props.size or (if fitContent then UDim2.new(1, 0, 0, 0) else UDim2.new(1, 0, 1, 0))
	local autoSize = props.autoSize or (if fitContent then Enum.AutomaticSize.Y else Enum.AutomaticSize.None)
	return React.createElement("frame", {
		BackgroundColor3 = QuickUtils.safeColor(backgroundColor),
		BackgroundTransparency = backgroundTransparency,
		Size = size,
		Position = props.position,
		AnchorPoint = props.anchorPoint,
		LayoutOrder = props.layoutOrder,
		BorderSizePixel = 0,
		ZIndex = props.zIndex,
		AutomaticSize = autoSize,
		ClipsDescendants = true,
	}, React.createElement("uilistlayout", {
		Padding = UDim.new(0, spacing),
		SortOrder = Enum.SortOrder.LayoutOrder,
		FillDirection = Enum.FillDirection.Vertical,
		HorizontalAlignment = props.horizontalAlignment or Enum.HorizontalAlignment.Left,
		VerticalAlignment = Enum.VerticalAlignment.Top,
	}), React.createElement("uipadding", {
		PaddingTop = UDim.new(0, padding),
		PaddingBottom = UDim.new(0, padding),
		PaddingLeft = UDim.new(0, padding),
		PaddingRight = UDim.new(0, padding),
	}), (props.minSize or props.maxSize) and (React.createElement("uisizeconstraint", {
		MinSize = if props.minSize then Vector2.new(props.minSize.X.Scale * 1920 + props.minSize.X.Offset, props.minSize.Y.Scale * 1080 + props.minSize.Y.Offset) else nil,
		MaxSize = if props.maxSize then Vector2.new(props.maxSize.X.Scale * 1920 + props.maxSize.X.Offset, props.maxSize.Y.Scale * 1080 + props.maxSize.Y.Offset) else nil,
	})), props.children)
end
return {
	VerticalFrame = VerticalFrame,
}
