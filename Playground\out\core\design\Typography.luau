-- Compiled with roblox-ts v3.0.0
local TYPOGRAPHY = {
	font = {
		primary = Enum.Font.Gotham,
		secondary = Enum.Font.GothamBold,
		heading = Enum.Font.FredokaOne,
		body = Enum.Font.Gotham,
		ui = Enum.Font.Gotham,
		mono = Enum.Font.RobotoMono,
	},
	weight = {
		regular = Enum.FontWeight.Regular,
		medium = Enum.FontWeight.Medium,
		semibold = Enum.FontWeight.SemiBold,
		bold = Enum.FontWeight.Bold,
		extrabold = Enum.FontWeight.ExtraBold,
	},
	fontFamily = {
		primary = Enum.Font.Gotham,
		display = Enum.Font.FredokaOne,
		playful = Enum.Font.Cartoon,
		readable = Enum.Font.SourceSans,
		technical = Enum.Font.RobotoMono,
	},
	size = {
		xs = 10,
		sm = 12,
		base = 14,
		lg = 16,
		xl = 18,
		["2xl"] = 20,
		["3xl"] = 24,
		["4xl"] = 28,
		["5xl"] = 32,
	},
	lineHeight = {
		tight = 1.1,
		normal = 1.4,
		relaxed = 1.6,
		loose = 1.8,
	},
	letterSpacing = {
		tight = -0.5,
		normal = 0,
		wide = 0.5,
		wider = 1,
	},
}
return {
	TYPOGRAPHY = TYPOGRAPHY,
}
