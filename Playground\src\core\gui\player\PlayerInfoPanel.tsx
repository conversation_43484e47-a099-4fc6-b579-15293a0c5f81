import * as React from "@rbxts/react";
import { Players } from "@rbxts/services";
import { COLORS, SIZES, BORDER_RADIUS, TYPOGRAPHY } from "../../design";
import { ResponsiveManager } from "../layout/ResponsiveManager";

interface PlayerInfoPanelProps {
	player?: Player;
	position?: UDim2;
	anchorPoint?: Vector2;
	size?: UDim2;
	zIndex?: number;
	
	// Display options
	showAvatar?: boolean;
	showLevel?: boolean;
	showStats?: boolean;
	showRobux?: boolean;
	
	// Roblox-style customization
	variant?: "primary" | "compact" | "detailed";
	
	// Responsive properties
	responsive?: boolean;
}

interface PlayerStats {
	level: number;
	experience: number;
	robux: number;
	playtime: string;
}

export function PlayerInfoPanel(props: PlayerInfoPanelProps): React.ReactElement {
	const player = props.player ?? Players.LocalPlayer;
	const [playerStats, setPlayerStats] = React.useState<PlayerStats>({
		level: 1,
		experience: 0,
		robux: 0,
		playtime: "0h 0m",
	});

	const responsiveManager = React.useMemo(() => {
		try {
			return ResponsiveManager.getInstance();
		} catch (error) {
			warn(`❌ Failed to get ResponsiveManager: ${error}`);
			return undefined;
		}
	}, []);

	// Mock player stats (in real implementation, fetch from data service)
	React.useEffect(() => {
		// Simulate loading player stats
		setPlayerStats({
			level: 25,
			experience: 1250,
			robux: 150,
			playtime: "2h 30m",
		});
	}, [player]);

	// Calculate responsive values
	const variant = props.variant ?? "primary";
	const showAvatar = props.showAvatar ?? true;
	const showLevel = props.showLevel ?? true;
	const showStats = props.showStats ?? (variant === "detailed");
	const showRobux = props.showRobux ?? (variant === "detailed");

	// Get variant-based sizing
	const getVariantSize = () => {
		switch (variant) {
			case "compact":
				return new UDim2(0, 200, 0, 60);
			case "detailed":
				return new UDim2(0, 280, 0, 120);
			case "primary":
			default:
				return new UDim2(0, 240, 0, 80);
		}
	};

	const size = props.size ?? getVariantSize();
	const avatarSize = variant === "compact" ? SIZES.avatar.small : SIZES.avatar.medium;

	return (
		<frame
			BackgroundColor3={Color3.fromHex(COLORS.roblox.panel)}
			BackgroundTransparency={COLORS.transparency.panel}
			Size={size}
			Position={props.position ?? new UDim2(0, 20, 0, 20)}
			AnchorPoint={props.anchorPoint ?? new Vector2(0, 0)}
			ZIndex={props.zIndex ?? 10}
			BorderSizePixel={0}
			ClipsDescendants={true}
		>
			{/* Rounded corners */}
			<uicorner CornerRadius={new UDim(0, BORDER_RADIUS.panel)} />
			
			{/* Border */}
			<uistroke
				Color={Color3.fromHex(COLORS.roblox["panel-border"])}
				Thickness={1}
				Transparency={COLORS.transparency["panel-border"]}
			/>

			{/* Drop shadow */}
			<frame
				Size={new UDim2(1, 4, 1, 4)}
				Position={new UDim2(0, 2, 0, 2)}
				AnchorPoint={new Vector2(0, 0)}
				BackgroundColor3={Color3.fromHex(COLORS.roblox["panel-shadow"])}
				BackgroundTransparency={COLORS.transparency["panel-shadow"]}
				BorderSizePixel={0}
				ZIndex={-1}
			>
				<uicorner CornerRadius={new UDim(0, BORDER_RADIUS.panel)} />
			</frame>

			{/* Content padding */}
			<uipadding
				PaddingTop={new UDim(0, 12)}
				PaddingBottom={new UDim(0, 12)}
				PaddingLeft={new UDim(0, 12)}
				PaddingRight={new UDim(0, 12)}
			/>

			{/* Main content layout */}
			<frame
				Size={new UDim2(1, 0, 1, 0)}
				BackgroundTransparency={1}
				BorderSizePixel={0}
			>
				{/* Avatar */}
				{showAvatar && (
					<frame
						Size={new UDim2(0, avatarSize, 0, avatarSize)}
						Position={new UDim2(0, 0, 0, 0)}
						BackgroundColor3={Color3.fromHex(COLORS.bg.avatar)}
						BorderSizePixel={0}
					>
						<uicorner CornerRadius={new UDim(0, BORDER_RADIUS.avatar)} />
						
						{/* Avatar image placeholder */}
						<textlabel
							Size={new UDim2(1, 0, 1, 0)}
							BackgroundTransparency={1}
							Text="👤"
							TextColor3={Color3.fromHex(COLORS.text.main)}
							TextSize={avatarSize * 0.6}
							Font={TYPOGRAPHY.fontFamily.primary}
							TextXAlignment={Enum.TextXAlignment.Center}
							TextYAlignment={Enum.TextYAlignment.Center}
						/>
					</frame>
				)}

				{/* Player info */}
				<frame
					Size={new UDim2(1, showAvatar ? -(avatarSize + 8) : 0, 1, 0)}
					Position={new UDim2(0, showAvatar ? avatarSize + 8 : 0, 0, 0)}
					BackgroundTransparency={1}
					BorderSizePixel={0}
				>
					{/* Player name */}
					<textlabel
						Size={new UDim2(1, 0, 0, 20)}
						Position={new UDim2(0, 0, 0, 0)}
						BackgroundTransparency={1}
						Text={player?.Name ?? "Player"}
						TextColor3={Color3.fromHex(COLORS.text.main)}
						TextSize={TYPOGRAPHY.fontSize.lg}
						Font={TYPOGRAPHY.fontFamily.secondary}
						TextXAlignment={Enum.TextXAlignment.Left}
						TextYAlignment={Enum.TextYAlignment.Center}
						TextTruncate={Enum.TextTruncate.AtEnd}
					/>

					{/* Level display */}
					{showLevel && (
						<frame
							Size={new UDim2(0, 60, 0, 16)}
							Position={new UDim2(0, 0, 0, 24)}
							BackgroundColor3={Color3.fromHex(COLORS.roblox.blue)}
							BorderSizePixel={0}
						>
							<uicorner CornerRadius={new UDim(0, 8)} />
							
							<textlabel
								Size={new UDim2(1, 0, 1, 0)}
								BackgroundTransparency={1}
								Text={`LV ${playerStats.level}`}
								TextColor3={Color3.fromHex(COLORS.text.main)}
								TextSize={TYPOGRAPHY.fontSize.sm}
								Font={TYPOGRAPHY.fontFamily.primary}
								TextXAlignment={Enum.TextXAlignment.Center}
								TextYAlignment={Enum.TextYAlignment.Center}
							/>
						</frame>
					)}

					{/* Stats section for detailed variant */}
					{showStats && variant === "detailed" && (
						<frame
							Size={new UDim2(1, 0, 0, 40)}
							Position={new UDim2(0, 0, 1, -40)}
							BackgroundTransparency={1}
							BorderSizePixel={0}
						>
							{/* Experience */}
							<textlabel
								Size={new UDim2(0.5, -4, 0, 18)}
								Position={new UDim2(0, 0, 0, 0)}
								BackgroundTransparency={1}
								Text={`XP: ${playerStats.experience}`}
								TextColor3={Color3.fromHex(COLORS.text.secondary)}
								TextSize={TYPOGRAPHY.fontSize.sm}
								Font={TYPOGRAPHY.fontFamily.primary}
								TextXAlignment={Enum.TextXAlignment.Left}
								TextYAlignment={Enum.TextYAlignment.Center}
							/>

							{/* Playtime */}
							<textlabel
								Size={new UDim2(0.5, -4, 0, 18)}
								Position={new UDim2(0.5, 4, 0, 0)}
								BackgroundTransparency={1}
								Text={`Time: ${playerStats.playtime}`}
								TextColor3={Color3.fromHex(COLORS.text.secondary)}
								TextSize={TYPOGRAPHY.fontSize.sm}
								Font={TYPOGRAPHY.fontFamily.primary}
								TextXAlignment={Enum.TextXAlignment.Left}
								TextYAlignment={Enum.TextYAlignment.Center}
							/>

							{/* Robux */}
							{showRobux && (
								<frame
									Size={new UDim2(0, 80, 0, 18)}
									Position={new UDim2(0, 0, 0, 22)}
									BackgroundColor3={Color3.fromHex(COLORS.roblox.green)}
									BorderSizePixel={0}
								>
									<uicorner CornerRadius={new UDim(0, 4)} />
									
									<textlabel
										Size={new UDim2(1, 0, 1, 0)}
										BackgroundTransparency={1}
										Text={`R$ ${playerStats.robux}`}
										TextColor3={Color3.fromHex(COLORS.text.main)}
										TextSize={TYPOGRAPHY.fontSize.sm}
										Font={TYPOGRAPHY.fontFamily.primary}
										TextXAlignment={Enum.TextXAlignment.Center}
										TextYAlignment={Enum.TextYAlignment.Center}
									/>
								</frame>
							)}
						</frame>
					)}
				</frame>
			</frame>
		</frame>
	);
}
