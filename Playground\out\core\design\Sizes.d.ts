export declare const SIZES: {
    padding: {
        xs: number;
        sm: number;
        md: number;
        lg: number;
        xl: number;
        "2xl": number;
    };
    margin: {
        xs: number;
        sm: number;
        md: number;
        lg: number;
        xl: number;
    };
    fontSize: {
        xs: number;
        sm: number;
        md: number;
        lg: number;
        xl: number;
        "2xl": number;
        "3xl": number;
        "4xl": number;
    };
    button: {
        width: number;
        height: number;
        minWidth: number;
        padding: number;
    };
    input: {
        width: number;
        height: number;
        padding: number;
    };
    gridCell: {
        width: number;
        height: number;
    };
    icon: {
        xs: number;
        sm: number;
        md: number;
        lg: number;
        xl: number;
    };
    modal: {
        small: {
            width: number;
            height: number;
        };
        medium: {
            width: number;
            height: number;
        };
        large: {
            width: number;
            height: number;
        };
    };
    card: {
        padding: number;
        gap: number;
    };
    avatar: {
        sm: number;
        md: number;
        lg: number;
        xl: number;
    };
    breakpoints: {
        mobile: number;
        tablet: number;
        desktop: number;
    };
};
