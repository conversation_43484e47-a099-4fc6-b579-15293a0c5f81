-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitFor<PERSON>hild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local createRoot = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react-roblox").createRoot
local _services = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "services")
local Players = _services.Players
local ReplicatedStorage = _services.ReplicatedStorage
local ElementalBattleUI = TS.import(script, script.Parent, "gui", "ElementalBattleUI").ElementalBattleUI
local MovementExample = TS.import(script, script.Parent, "movement", "MovementExample").MovementExample
local _core = TS.import(script, game:GetService("ReplicatedStorage"), "core")
local initializeDebugSystem = _core.initializeDebugSystem
local initializeClientCore = _core.initializeClientCore
local SplashScreen = _core.SplashScreen
local useSplashScreen = _core.useSplashScreen
local ToastManager = _core.ToastManager
local ToastService = _core.ToastService
local ErrorBoundary = _core.ErrorBoundary
local COLORS = _core.COLORS
local SIZES = _core.SIZES
local BORDER_RADIUS = _core.BORDER_RADIUS
local QuickUtils = TS.import(script, game:GetService("ReplicatedStorage"), "core", "helper", "GameUtils").QuickUtils
local version = "v1.3.5"
local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")
-- Create a ScreenGui with properties to make it visible
local screenGui = Instance.new("ScreenGui", playerGui)
screenGui.ResetOnSpawn = false
screenGui.IgnoreGuiInset = true
screenGui.ZIndexBehavior = Enum.ZIndexBehavior.Sibling
screenGui.DisplayOrder = 100
screenGui.Name = "MainReactGUI"
print(`🎮 [{tick()}] Main React ScreenGui created with DisplayOrder: {screenGui.DisplayOrder}`)
-- Main App Component with Splash Screen - Focus on Elemental Battle Game
local function MainApp()
	local _binding = useSplashScreen()
	local state = _binding.state
	local startLoading = _binding.startLoading
	local hide = _binding.hide
	local setupDefaultTasks = _binding.setupDefaultTasks
	local manager = _binding.manager
	local hasStarted, setHasStarted = React.useState(false)
	local gameUIOpen, setGameUIOpen = React.useState(false)
	-- Setup and start loading when component mounts
	React.useEffect(function()
		if not hasStarted then
			print("🚀 MainApp: Starting initialization process...")
			-- Setup default core framework loading tasks
			setupDefaultTasks()
			-- Add custom game loading tasks with better error handling
			manager:addLoadingTask({
				name = "Waiting for Server...",
				weight = 1,
				task = TS.async(function()
					TS.try(function()
						print("🔍 Waiting for server to be ready...")
						-- Wait for server initialization indicator
						local serverIndicator = ReplicatedStorage:WaitForChild("CoreServerInitialized", 30)
						if serverIndicator and serverIndicator.Value then
							print("✅ Server is ready!")
						else
							warn("⚠️ Server not ready, but continuing...")
						end
					end, function(error)
						warn(`⚠️ Failed to detect server readiness: {error}`)
						-- Continue anyway
					end)
				end),
			})
			manager:addLoadingTask({
				name = "Loading Game Systems...",
				weight = 2,
				task = TS.async(function()
					TS.try(function()
						-- Initialize debug system for development
						print("🔧 Initializing debug system...")
						initializeDebugSystem()
						-- Initialize client core
						print("🏗️ Initializing client core...")
						local clientCoreResult = TS.await(initializeClientCore())
						if clientCoreResult:isError() then
							local errorMessage = `Failed to initialize client core: {clientCoreResult:getError().message}`
							warn(errorMessage)
							error(errorMessage)
						end
						print("✅ Game systems loaded successfully")
					end, function(error)
						warn(`⚠️ Game systems loading failed: {error}`)
						-- Don't throw - allow the app to continue with limited functionality
					end)
				end),
			})
			manager:addLoadingTask({
				name = "Initializing Movement System...",
				weight = 1,
				task = function()
					TS.try(function()
						-- Initialize movement example for testing
						print("🏃 Initializing movement system...")
						MovementExample.new()
						print("✅ Movement system initialized")
					end, function(error)
						warn(`⚠️ Movement system initialization failed: {error}`)
						-- Continue without movement - not critical for UI
					end)
				end,
			})
			manager:addLoadingTask({
				name = "Finalizing Elemental Battle Setup...",
				weight = 1,
				task = TS.async(function()
					TS.try(function()
						print(`⚔️ Elemental Battle Arena loaded! [{version}]`)
						print(`🎮 Press Enter or click the floating button to open the game menu`)
						-- Validate critical systems are working
						local players = game:GetService("Players")
						local localPlayer = players.LocalPlayer
						if not localPlayer then
							warn("⚠️ LocalPlayer not available - some features may not work")
						end
						-- Show success toast when loading is complete (with delay to ensure toast system is ready)
						task.delay(0.5, function()
							TS.try(function()
								ToastService.showSuccess("⚔️ Elemental Battle Arena", `Ready for epic battles! v{version}`)
							end, function(toastError)
								warn(`⚠️ Failed to show welcome toast: {toastError}`)
							end)
						end)
						print("🎯 Elemental Battle client setup finalized successfully")
					end, function(error)
						warn(`⚠️ Client setup finalization failed: {error}`)
						-- Continue anyway - basic functionality should still work
					end)
				end),
			})
			-- Start the loading process
			print("🎬 Starting loading process...")
			startLoading():catch(function(loadingError)
				warn(`❌ Loading process failed: {loadingError}`)
				-- Force hide splash screen if loading completely fails
				task.delay(2, function()
					hide()
					ToastService.showError("Loading Error", "Some systems failed to load, but the application is still functional.")
				end)
			end)
			setHasStarted(true)
		end
	end, { hasStarted, setupDefaultTasks, manager, startLoading })
	local handleLoadingComplete = React.useCallback(function()
		-- Called when loading is complete and splash screen should hide
		hide()
		print("🎉 Splash screen loading completed!")
		-- Auto-open the game UI after loading
		task.delay(1, function()
			setGameUIOpen(true)
		end)
	end, { hide })
	-- Handle keyboard input for opening game UI
	React.useEffect(function()
		local UserInputService = game:GetService("UserInputService")
		local connection = UserInputService.InputBegan:Connect(function(input, gameProcessed)
			if gameProcessed then
				return nil
			end
			if input.UserInputType == Enum.UserInputType.Keyboard then
				if input.KeyCode == Enum.KeyCode.Return then
					-- Enter key
					setGameUIOpen(true)
				end
			end
		end)
		return function()
			return connection:Disconnect()
		end
	end, {})
	return React.createElement(ErrorBoundary, {
		onError = function(err, errorId)
			warn(`[MainApp] Critical error in main application: {err} (ID: {errorId})`)
			-- Don't crash the entire app - show a toast notification instead
			TS.try(function()
				ToastService.showError("Application Error", `A component error occurred. Error ID: {errorId}`)
			end, function(toastError)
				warn(`[MainApp] Failed to show error toast: {toastError}`)
			end)
		end,
	}, React.createElement(React.Fragment, nil, React.createElement(ErrorBoundary, {
		onError = function(err, errorId)
			warn(`[SplashScreen] Error in splash screen: {err} (ID: {errorId})`)
		end,
	}, React.createElement(SplashScreen, {
		isVisible = state.isVisible,
		loadingProgress = state.loadingProgress,
		loadingText = state.loadingText,
		onLoadingComplete = handleLoadingComplete,
	})), React.createElement(ErrorBoundary, {
		onError = function(err, errorId)
			warn(`[ToastManager] Error in toast system: {err} (ID: {errorId})`)
		end,
	}, React.createElement(ToastManager)), not state.isVisible and (React.createElement(React.Fragment, nil, React.createElement(ErrorBoundary, {
		onError = function(err, errorId)
			warn(`[FloatingButton] Error in floating button: {err} (ID: {errorId})`)
		end,
	}, React.createElement("frame", {
		Size = UDim2.new(0, 90, 0, 90),
		Position = UDim2.new(0, 20, 1, -110),
		AnchorPoint = Vector2.new(0, 1),
		BackgroundColor3 = QuickUtils.safeColor(COLORS.primary),
		BorderSizePixel = 0,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, 45),
	}), " ", React.createElement("uistroke", {
		Color = QuickUtils.safeColor(COLORS.warning),
		Thickness = 3,
	}), " ", React.createElement("frame", {
		Size = UDim2.new(1, 6, 1, 6),
		Position = UDim2.new(0, 3, 0, 3),
		AnchorPoint = Vector2.new(0, 0),
		BackgroundColor3 = Color3.fromRGB(0, 0, 0),
		BackgroundTransparency = 0.8,
		BorderSizePixel = 0,
		ZIndex = -1,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, 48),
	})), React.createElement("textbutton", {
		Size = UDim2.new(1, 0, 1, 0),
		Position = UDim2.new(0, 0, 0, 0),
		BackgroundTransparency = 1,
		Text = "⚔️",
		TextSize = 36,
		TextColor3 = QuickUtils.safeColor(COLORS.text.inverse),
		Font = Enum.Font.FredokaOne,
		Event = {
			MouseButton1Click = function()
				return setGameUIOpen(true)
			end,
			MouseEnter = function(rbx)
				-- Hover effect - cast to frame to access BackgroundColor3
				local frame = rbx.Parent
				if frame then
					frame.BackgroundColor3 = QuickUtils.safeColor(COLORS["primary-hover"])
				end
			end,
			MouseLeave = function(rbx)
				-- Reset color
				local frame = rbx.Parent
				if frame then
					frame.BackgroundColor3 = QuickUtils.safeColor(COLORS.primary)
				end
			end,
		},
	}), React.createElement("textlabel", {
		Size = UDim2.new(0, 220, 0, 24),
		Position = UDim2.new(1, 15, 0.5, -12),
		AnchorPoint = Vector2.new(0, 0.5),
		BackgroundColor3 = QuickUtils.safeColor(COLORS.bg.base),
		BackgroundTransparency = 0.1,
		BorderSizePixel = 0,
		Text = "⚔️ Elemental Battle Arena",
		TextSize = SIZES.fontSize.md,
		TextColor3 = QuickUtils.safeColor(COLORS.text.main),
		Font = Enum.Font.Gotham,
		TextXAlignment = Enum.TextXAlignment.Left,
		TextStrokeTransparency = 1,
	}, React.createElement("uicorner", {
		CornerRadius = UDim.new(0, BORDER_RADIUS.sm),
	}), React.createElement("uistroke", {
		Color = QuickUtils.safeColor(COLORS.border.base),
		Thickness = 1,
		Transparency = 0.5,
	}), React.createElement("uipadding", {
		PaddingLeft = UDim.new(0, SIZES.padding.sm),
		PaddingRight = UDim.new(0, SIZES.padding.sm),
	})))), React.createElement(ErrorBoundary, {
		onError = function(err, errorId)
			warn(`[ElementalBattleUI] Error in elemental battle UI: {err} (ID: {errorId})`)
			TS.try(function()
				ToastService.showError("Game UI Error", "The game interface encountered an error.")
			end, function(toastError)
				warn(`[ElementalBattleUI] Failed to show error toast: {toastError}`)
			end)
		end,
	}, React.createElement(ElementalBattleUI, {
		isOpen = gameUIOpen,
		onClose = function()
			return setGameUIOpen(false)
		end,
	}))))))
end
local root = createRoot(screenGui)
-- Render the main app with splash screen
root:render(React.createElement(MainApp))
-- All initialization is now handled by the splash screen system
