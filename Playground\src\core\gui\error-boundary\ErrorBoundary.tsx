import * as React from "@rbxts/react";

interface ErrorBoundaryProps {
	children?: React.ReactNode;
	fallback?: (err: unknown, errorId: string, retry: () => void) => React.ReactElement;
	onError?: (err: unknown, errorId: string) => void;
}

// Simple functional error boundary that catches errors in children
export function ErrorBoundary(props: ErrorBoundaryProps): React.ReactNode {
	const [hasError, setHasError] = React.useState(false);
	const [errorData, setErrorData] = React.useState<unknown>(undefined);
	const [errorId, setErrorId] = React.useState<string>("");
	const errorIdCounterRef = React.useRef(0);

	// Reset error state when children change
	React.useEffect(() => {
		if (hasError) {
			setHasError(false);
			setErrorData(undefined);
			setErrorId("");
		}
	}, [props.children]);

	const handleRetry = React.useCallback(() => {
		setHasError(false);
		setErrorData(undefined);
		setErrorId("");
	}, []);

	// Error boundary logic using try-catch in useEffect
	const renderChildren = React.useMemo(() => {
		if (hasError) {
			return undefined; // Don't render children if there's an error
		}

		try {
			return props.children;
		} catch (err) {
			// Catch synchronous errors
			const errorMessage = typeIs(err, "string") ? err : tostring(err);

			// Generate error ID safely
			errorIdCounterRef.current = errorIdCounterRef.current + 1;
			const newErrorId = `error_boundary_${errorIdCounterRef.current}_${tick()}`;

			// Simple logging without dependencies
			warn(`[ErrorBoundary] React component error: ${errorMessage}`);
			warn(`[ErrorBoundary] Error ID: ${newErrorId}`);

			// Set error state
			setHasError(true);
			setErrorData(err);
			setErrorId(newErrorId);

			// Call custom error handler if provided
			if (props.onError) {
				try {
					props.onError(err, newErrorId);
				} catch (handlerError) {
					warn(`[ErrorBoundary] Error handler failed: ${handlerError}`);
				}
			}

			return undefined;
		}
	}, [props.children, hasError, props.onError]);

	if (hasError) {
		// Use custom fallback if provided
		if (props.fallback && errorId !== "") {
			try {
				return props.fallback(errorData, errorId, handleRetry);
			} catch (fallbackError) {
				warn(`[ErrorBoundary] Custom fallback failed: ${fallbackError}`);
				// Fall through to default UI
			}
		}

		// Safe default error UI that won't cause more errors
		return (
			<frame
				Size={new UDim2(1, 0, 1, 0)}
				Position={new UDim2(0, 0, 0, 0)}
				BackgroundColor3={Color3.fromHex("#2a2a2a")}
				BackgroundTransparency={0}
				BorderSizePixel={2}
				BorderColor3={Color3.fromHex("#ff4444")}
			>
				<uicorner CornerRadius={new UDim(0, 8)} />
				<uipadding
					PaddingTop={new UDim(0, 16)}
					PaddingBottom={new UDim(0, 16)}
					PaddingLeft={new UDim(0, 16)}
					PaddingRight={new UDim(0, 16)}
				/>

				<uilistlayout
					SortOrder={Enum.SortOrder.LayoutOrder}
					FillDirection={Enum.FillDirection.Vertical}
					HorizontalAlignment={Enum.HorizontalAlignment.Center}
					VerticalAlignment={Enum.VerticalAlignment.Center}
					Padding={new UDim(0, 8)}
				/>

				<textlabel
					Text="🚨 Component Error"
					Size={new UDim2(1, 0, 0, 24)}
					BackgroundTransparency={1}
					TextColor3={Color3.fromHex("#ff4444")}
					TextSize={18}
					Font={Enum.Font.SourceSansBold}
					TextXAlignment={Enum.TextXAlignment.Center}
					LayoutOrder={1}
				/>

				<textlabel
					Text="This component encountered an error and has been safely contained."
					Size={new UDim2(1, 0, 0, 40)}
					BackgroundTransparency={1}
					TextColor3={Color3.fromHex("#cccccc")}
					TextSize={14}
					Font={Enum.Font.SourceSans}
					TextXAlignment={Enum.TextXAlignment.Center}
					TextWrapped={true}
					LayoutOrder={2}
				/>

				{errorId !== undefined && errorId !== "" ? (
					<textlabel
						Text={`Error ID: ${errorId}`}
						Size={new UDim2(1, 0, 0, 16)}
						BackgroundTransparency={1}
						TextColor3={Color3.fromHex("#888888")}
						TextSize={12}
						Font={Enum.Font.SourceSans}
						TextXAlignment={Enum.TextXAlignment.Center}
						LayoutOrder={3}
					/>
				) : undefined}

				<textbutton
					Text="Try Again"
					Size={new UDim2(0, 100, 0, 32)}
					BackgroundColor3={Color3.fromHex("#4a9eff")}
					BorderSizePixel={0}
					TextColor3={Color3.fromHex("#ffffff")}
					TextSize={14}
					Font={Enum.Font.SourceSansBold}
					LayoutOrder={4}
					Event={{
						Activated: handleRetry,
					}}
				>
					<uicorner CornerRadius={new UDim(0, 4)} />
				</textbutton>
			</frame>
		);
	}

	// Render children if no error - return them directly without wrapping
	return renderChildren;
}
