-- Compiled with roblox-ts v3.0.0
local TS = require(game:GetService("ReplicatedStorage"):WaitForChild("rbxts_include"):WaitForChild("RuntimeLib"))
local React = TS.import(script, game:GetService("ReplicatedStorage"), "rbxts_include", "node_modules", "@rbxts", "react")
local COLORS = TS.import(script, game:GetService("ReplicatedStorage"), "core", "design").COLORS
local QuickUtils = TS.import(script, game:GetService("ReplicatedStorage"), "core", "helper", "GameUtils").QuickUtils
local LoadingSpinner = React.memo(function(props)
	local _condition = props.size
	if _condition == nil then
		_condition = 20
	end
	local size = _condition
	local _condition_1 = props.color
	if _condition_1 == nil then
		_condition_1 = COLORS.primary
	end
	local color = _condition_1
	local _condition_2 = props.speed
	if _condition_2 == nil then
		_condition_2 = 1
	end
	local speed = _condition_2
	return React.createElement("textlabel", {
		Size = UDim2.new(0, size, 0, size),
		BackgroundTransparency = 1,
		Text = "●",
		TextColor3 = QuickUtils.safeColor(color),
		TextSize = size,
		Font = Enum.Font.Gotham,
		TextXAlignment = Enum.TextXAlignment.Center,
		TextYAlignment = Enum.TextYAlignment.Center,
	})
end)
local function LoadingIndicator(props)
	local currentSize = {
		spinner = if props.size == "lg" then 24 elseif props.size == "sm" then 16 else 20,
		text = if props.size == "lg" then 18 elseif props.size == "sm" then 12 else 14,
	}
	local _attributes = {}
	local _exp = currentSize.spinner
	local _value = props.text
	_attributes.Size = UDim2.new(0, 0, 0, _exp + (if _value ~= "" and _value then currentSize.text + 10 else 0))
	_attributes.BackgroundTransparency = 1
	_attributes.AutomaticSize = Enum.AutomaticSize.X
	local _exp_1 = React.createElement("uilistlayout", {
		FillDirection = Enum.FillDirection.Vertical,
		HorizontalAlignment = Enum.HorizontalAlignment.Center,
		VerticalAlignment = Enum.VerticalAlignment.Center,
		Padding = UDim.new(0, 4),
	})
	local _exp_2 = React.createElement(LoadingSpinner, {
		size = currentSize.spinner,
		speed = 1,
	})
	local _value_1 = props.text
	return React.createElement("frame", _attributes, _exp_1, _exp_2, if _value_1 ~= "" and _value_1 then (React.createElement("textlabel", {
		Size = UDim2.new(0, 0, 0, currentSize.text),
		BackgroundTransparency = 1,
		Text = props.text,
		TextSize = currentSize.text,
		Font = Enum.Font.Gotham,
		TextColor3 = QuickUtils.safeColor(COLORS.text.main),
		AutomaticSize = Enum.AutomaticSize.X,
	})) else nil)
end
return {
	LoadingIndicator = LoadingIndicator,
	LoadingSpinner = LoadingSpinner,
}
