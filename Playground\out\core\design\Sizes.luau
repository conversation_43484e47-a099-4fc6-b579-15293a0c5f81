-- Compiled with roblox-ts v3.0.0
local SIZES = {
	padding = {
		xs = 4,
		sm = 8,
		md = 16,
		lg = 24,
		xl = 32,
		["2xl"] = 48,
	},
	margin = {
		xs = 4,
		sm = 8,
		md = 16,
		lg = 24,
		xl = 32,
	},
	fontSize = {
		xs = 10,
		sm = 12,
		md = 14,
		lg = 16,
		xl = 18,
		["2xl"] = 20,
		["3xl"] = 24,
		["4xl"] = 28,
	},
	button = {
		width = 140,
		height = 44,
		minWidth = 100,
		padding = 16,
	},
	input = {
		width = 220,
		height = 40,
		padding = 12,
	},
	gridCell = {
		width = 60,
		height = 60,
	},
	icon = {
		xs = 12,
		sm = 16,
		md = 20,
		lg = 24,
		xl = 32,
	},
	modal = {
		small = {
			width = 400,
			height = 300,
		},
		medium = {
			width = 600,
			height = 500,
		},
		large = {
			width = 800,
			height = 700,
		},
	},
	card = {
		padding = 20,
		gap = 12,
	},
	avatar = {
		sm = 32,
		md = 48,
		lg = 64,
		xl = 96,
	},
	breakpoints = {
		mobile = 768,
		tablet = 1024,
		desktop = 1280,
	},
}
return {
	SIZES = SIZES,
}
